<?php
require_once 'includes/functions.php';
include 'includes/header.php';

// Get action from query string
$action = isset($_GET['action']) ? $_GET['action'] : 'list';
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add' || $action === 'edit') {
        // Get form data
        $subject_id = intval($_POST['subject_id']);
        $academic_year = sanitizeInput($_POST['academic_year']);
        $semester = sanitizeInput($_POST['semester']);
        $exam_date = sanitizeInput($_POST['exam_date']);
        
        // Validate data
        $errors = [];
        if ($subject_id <= 0) {
            $errors[] = 'يجب اختيار المادة';
        }
        if (empty($academic_year)) {
            $errors[] = 'يجب إدخال العام الدراسي';
        }
        if (empty($semester)) {
            $errors[] = 'يجب اختيار الفصل الدراسي';
        }
        if (empty($exam_date)) {
            $errors[] = 'يجب إدخال تاريخ الاختبار';
        }
        
        if (empty($errors)) {
            $conn = connectDB();
            
            // For adding a new exam
            if ($action === 'add') {
                $sql = "INSERT INTO exams (subject_id, academic_year, semester, exam_date) 
                       VALUES (?, ?, ?, ?)";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param('isss', $subject_id, $academic_year, $semester, $exam_date);
                $stmt->execute();
                $stmt->close();
                
                $_SESSION['success_message'] = 'تمت إضافة الاختبار بنجاح';
                header('Location: exams.php');
                exit;
            } 
            // For updating an existing exam
            else if ($action === 'edit' && $id > 0) {
                $sql = "UPDATE exams SET subject_id = ?, academic_year = ?, semester = ?, exam_date = ? 
                       WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param('isssi', $subject_id, $academic_year, $semester, $exam_date, $id);
                $stmt->execute();
                $stmt->close();
                
                $_SESSION['success_message'] = 'تم تحديث الاختبار بنجاح';
                header('Location: exams.php');
                exit;
            }
            
            closeDB($conn);
        } else {
            $_SESSION['error_message'] = implode('<br>', $errors);
        }
    } 
    // Handle delete action
    else if ($action === 'delete' && $id > 0) {
        $conn = connectDB();
        
        // Delete the exam record
        $sql = "DELETE FROM exams WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('i', $id);
        $stmt->execute();
        $stmt->close();
        
        closeDB($conn);
        
        $_SESSION['success_message'] = 'تم حذف الاختبار بنجاح';
        header('Location: exams.php');
        exit;
    }
}

// Get data for display
$conn = connectDB();

// For edit action
$exam = null;
if ($action === 'edit' && $id > 0) {
    $sql = "SELECT e.*, s.name as subject_name 
           FROM exams e 
           JOIN subjects s ON e.subject_id = s.id 
           WHERE e.id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $result = $stmt->get_result();
    $exam = $result->fetch_assoc();
    $stmt->close();
    
    if (!$exam) {
        $_SESSION['error_message'] = 'الاختبار غير موجود';
        header('Location: exams.php');
        exit;
    }
}

// For list action
$exams = [];
if ($action === 'list') {
    // Get filter values
    $filter_year = isset($_GET['academic_year']) ? sanitizeInput($_GET['academic_year']) : '';
    $filter_semester = isset($_GET['semester']) ? sanitizeInput($_GET['semester']) : '';
    
    // Build the query
    $sql = "SELECT e.*, s.name as subject_name, st.name as stage_name, l.name as level_name 
           FROM exams e 
           JOIN subjects s ON e.subject_id = s.id 
           JOIN stages st ON s.stage_id = st.id 
           JOIN levels l ON s.level_id = l.id 
           WHERE 1=1";
    
    $params = [];
    $types = '';
    
    if (!empty($filter_year)) {
        $sql .= " AND e.academic_year = ?";
        $params[] = $filter_year;
        $types .= 's';
    }
    
    if (!empty($filter_semester)) {
        $sql .= " AND e.semester = ?";
        $params[] = $filter_semester;
        $types .= 's';
    }
    
    $sql .= " ORDER BY e.exam_date DESC";
    
    $stmt = $conn->prepare($sql);
    
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        $exams[] = $row;
    }
    
    $stmt->close();
}

// Get all subjects for dropdown
$sql = "SELECT s.*, st.name as stage_name, l.name as level_name 
       FROM subjects s 
       JOIN stages st ON s.stage_id = st.id 
       JOIN levels l ON s.level_id = l.id 
       ORDER BY st.id, l.id, s.name";
$result = $conn->query($sql);
$subjects = [];
while ($row = $result->fetch_assoc()) {
    $subjects[] = $row;
}

// Get all academic years for filter
$academicYears = getAcademicYears();
if (empty($academicYears)) {
    $academicYears[] = getCurrentAcademicYear();
}

closeDB($conn);
?>

<?php if ($action === 'list'): ?>
<!-- Exams List -->
<div class="page-header">
    <h1 class="page-title">إدارة الاختبارات</h1>
    <div class="page-actions">
        <a href="exams.php?action=add" class="btn btn-primary"><i class="fas fa-plus-circle"></i> إضافة اختبار جديد</a>
    </div>
</div>

<!-- Filters -->
<div class="form-container">
    <h2 class="form-title">تصفية النتائج</h2>
    <form action="exams.php" method="get" class="filter-form">
        <div class="form-row">
            <div class="form-col">
                <label for="academic_year" class="form-label">العام الدراسي</label>
                <select name="academic_year" id="academic_year" class="form-control" onchange="this.form.submit()">
                    <option value="">الكل</option>
                    <?php foreach ($academicYears as $year): ?>
                    <option value="<?php echo $year; ?>" <?php echo ($filter_year == $year) ? 'selected' : ''; ?>>
                        <?php echo $year; ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-col">
                <label for="semester" class="form-label">الفصل الدراسي</label>
                <select name="semester" id="semester" class="form-control" onchange="this.form.submit()">
                    <option value="">الكل</option>
                    <option value="first" <?php echo ($filter_semester == 'first') ? 'selected' : ''; ?>>الفصل الأول</option>
                    <option value="second" <?php echo ($filter_semester == 'second') ? 'selected' : ''; ?>>الفصل الثاني</option>
                    <option value="final" <?php echo ($filter_semester == 'final') ? 'selected' : ''; ?>>النهائي</option>
                </select>
            </div>
        </div>
    </form>
</div>

<!-- Exams Table -->
<div class="table-container">
    <div class="table-header">
        <h2 class="table-title">قائمة الاختبارات</h2>
        <div class="table-actions">
            <input type="text" class="table-search-input form-control" placeholder="بحث...">
        </div>
    </div>
    
    <?php if (count($exams) > 0): ?>
    <table class="data-table">
        <thead>
            <tr>
                <th data-sortable="true">المادة</th>
                <th data-sortable="true">المرحلة</th>
                <th data-sortable="true">المستوى</th>
                <th data-sortable="true">العام الدراسي</th>
                <th data-sortable="true">الفصل الدراسي</th>
                <th data-sortable="true">تاريخ الاختبار</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($exams as $exam): ?>
            <tr>
                <td><?php echo $exam['subject_name']; ?></td>
                <td><?php echo $exam['stage_name']; ?></td>
                <td><?php echo $exam['level_name']; ?></td>
                <td><?php echo $exam['academic_year']; ?></td>
                <td>
                    <?php 
                    if ($exam['semester'] === 'first') echo 'الفصل الأول';
                    elseif ($exam['semester'] === 'second') echo 'الفصل الثاني';
                    else echo 'النهائي';
                    ?>
                </td>
                <td><?php echo date('Y-m-d', strtotime($exam['exam_date'])); ?></td>
                <td class="action-buttons">
                    <a href="results.php?exam_id=<?php echo $exam['id']; ?>" class="btn btn-info btn-sm" title="عرض النتائج"><i class="fas fa-chart-bar"></i></a>
                    <a href="exams.php?action=edit&id=<?php echo $exam['id']; ?>" class="btn btn-secondary btn-sm" title="تعديل"><i class="fas fa-edit"></i></a>
                    <a href="exams.php?action=delete&id=<?php echo $exam['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirmDelete(event);" title="حذف"><i class="fas fa-trash"></i></a>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
    <?php else: ?>
    <p class="no-data">لا توجد اختبارات متطابقة مع معايير البحث</p>
    <?php endif; ?>
</div>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
<!-- Add/Edit Exam Form -->
<div class="page-header">
    <h1 class="page-title"><?php echo ($action === 'add') ? 'إضافة اختبار جديد' : 'تعديل بيانات الاختبار'; ?></h1>
    <div class="page-actions">
        <a href="exams.php" class="btn btn-secondary"><i class="fas fa-arrow-right"></i> العودة للقائمة</a>
    </div>
</div>

<div class="form-container">
    <form action="exams.php?action=<?php echo $action; ?><?php echo ($id > 0) ? '&id=' . $id : ''; ?>" method="post" class="needs-validation">
        <div class="form-row">
            <div class="form-col">
                <label for="subject_id" class="form-label">المادة *</label>
                <select name="subject_id" id="subject_id" class="form-control" required>
                    <option value="">اختر المادة</option>
                    <?php foreach ($subjects as $subject): ?>
                    <option value="<?php echo $subject['id']; ?>" <?php echo ($action === 'edit' && $exam['subject_id'] == $subject['id']) ? 'selected' : ''; ?>>
                        <?php echo $subject['name'] . ' - ' . $subject['stage_name'] . ' - ' . $subject['level_name']; ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-col">
                <label for="academic_year" class="form-label">العام الدراسي *</label>
                <input type="text" class="form-control" id="academic_year" name="academic_year" value="<?php echo ($action === 'edit') ? $exam['academic_year'] : getCurrentAcademicYear(); ?>" required>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-col">
                <label for="semester" class="form-label">الفصل الدراسي *</label>
                <select name="semester" id="semester" class="form-control" required>
                    <option value="">اختر الفصل الدراسي</option>
                    <option value="first" <?php echo ($action === 'edit' && $exam['semester'] === 'first') ? 'selected' : ''; ?>>الفصل الأول</option>
                    <option value="second" <?php echo ($action === 'edit' && $exam['semester'] === 'second') ? 'selected' : ''; ?>>الفصل الثاني</option>
                    <option value="final" <?php echo ($action === 'edit' && $exam['semester'] === 'final') ? 'selected' : ''; ?>>النهائي</option>
                </select>
            </div>
            
            <div class="form-col">
                <label for="exam_date" class="form-label">تاريخ الاختبار *</label>
                <input type="date" class="form-control" id="exam_date" name="exam_date" value="<?php echo ($action === 'edit') ? $exam['exam_date'] : date('Y-m-d'); ?>" required>
            </div>
        </div>
        
        <div class="form-group mt-4">
            <button type="submit" class="btn btn-primary"><?php echo ($action === 'add') ? 'إضافة الاختبار' : 'تحديث البيانات'; ?></button>
            <a href="exams.php" class="btn btn-secondary">إلغاء</a>
        </div>
    </form>
</div>
<?php endif; ?>

<?php include 'includes/footer.php'; ?> 