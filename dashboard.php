<?php
require_once 'includes/functions.php';
include 'includes/header.php';

// Get counts for dashboard
$conn = connectDB();

// في وضع المعاينة، نستخدم بيانات وهمية
if (defined('PREVIEW_MODE') && PREVIEW_MODE) {
    // بيانات وهمية للوحة التحكم
    $totalStudents = 3; // عدد الطلاب في البيانات الوهمية
    $academicYear = getCurrentAcademicYear();
    $totalExams = 5; // عدد وهمي للاختبارات
    
    // إحصائيات المراحل
    $stages = getAllStages();
    $stageStats = [];
    foreach ($stages as $stage) {
        $count = 0;
        // حساب عدد الطلاب في كل مرحلة من البيانات الوهمية
        if ($stage['id'] == 1) $count = 1; // مرحلة تمهيدية
        elseif ($stage['id'] == 2) $count = 1; // مرحلة متوسطة
        elseif ($stage['id'] == 3) $count = 1; // مرحلة تخصصية
        
        $stageStats[$stage['id']] = [
            'name' => $stage['name'],
            'count' => $count
        ];
    }
    
    // بيانات وهمية لأحدث النتائج
    $recentResults = [
        [
            'id' => 1,
            'student_name' => 'أحمد محمد علي',
            'subject_name' => 'القرآن الكريم',
            'mark' => 85,
            'created_at' => date('Y-m-d H:i:s')
        ],
        [
            'id' => 2,
            'student_name' => 'محمود أحمد سعيد',
            'subject_name' => 'اللغة العربية',
            'mark' => 92,
            'created_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
        ],
        [
            'id' => 3,
            'student_name' => 'خالد عبدالله محمد',
            'subject_name' => 'الفقه',
            'mark' => 78,
            'created_at' => date('Y-m-d H:i:s', strtotime('-2 days'))
        ]
    ];
} else {
    // حالة الاتصال الفعلي بقاعدة البيانات (عندما لا نكون في وضع المعاينة)
    
    // Total students count
    $sql = "SELECT COUNT(*) as count FROM students WHERE status = 'active'";
    $result = $conn->query($sql);
    $countData = $result->fetch_assoc();
    $totalStudents = $countData ? $countData['count'] : 0;
    
    // Students by stage
    $stages = getAllStages();
    $stageStats = [];
    foreach ($stages as $stage) {
        $sql = "SELECT COUNT(*) as count FROM students WHERE stage_id = ? AND status = 'active'";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('i', $stage['id']);
        $stmt->execute();
        $result = $stmt->get_result();
        $countData = $result->fetch_assoc();
        $count = $countData ? $countData['count'] : 0;
        
        $stageStats[$stage['id']] = [
            'name' => $stage['name'],
            'count' => $count
        ];
        $stmt->close();
    }
    
    // Total exams count for current academic year
    $academicYear = getCurrentAcademicYear();
    $sql = "SELECT COUNT(*) as count FROM exams WHERE academic_year = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('s', $academicYear);
    $stmt->execute();
    $result = $stmt->get_result();
    $countData = $result->fetch_assoc();
    $totalExams = $countData ? $countData['count'] : 0;
    $stmt->close();
    
    // Recent results (latest 10)
    $sql = "SELECT er.*, s.full_name as student_name, sub.name as subject_name
           FROM exam_results er
           JOIN students st ON er.student_id = st.id
           JOIN users s ON st.user_id = s.id
           JOIN subjects sub ON er.subject_id = sub.id
           JOIN exams e ON er.exam_id = e.id
           ORDER BY er.created_at DESC
           LIMIT 10";
    $result = $conn->query($sql);
    $recentResults = [];
    while ($row = $result->fetch_assoc()) {
        $recentResults[] = $row;
    }
}

closeDB($conn);
?>

<h1 class="page-title">لوحة التحكم</h1>

<!-- Stats Cards -->
<div class="dashboard-stats">
    <div class="stat-card">
        <h3>إجمالي الطلاب</h3>
        <div class="stat-number"><?php echo $totalStudents; ?></div>
    </div>
    
    <?php foreach ($stageStats as $stat): ?>
    <div class="stat-card">
        <h3>طلاب المرحلة <?php echo $stat['name']; ?></h3>
        <div class="stat-number"><?php echo $stat['count']; ?></div>
    </div>
    <?php endforeach; ?>
    
    <div class="stat-card">
        <h3>الاختبارات (<?php echo $academicYear; ?>)</h3>
        <div class="stat-number"><?php echo $totalExams; ?></div>
    </div>
</div>

<!-- Quick Actions -->
<div class="form-container">
    <h2 class="form-title">إجراءات سريعة</h2>
    <div class="quick-actions">
        <a href="students.php?action=add" class="btn btn-primary"><i class="fas fa-user-plus"></i> إضافة طالب جديد</a>
        <a href="exams.php?action=add" class="btn btn-primary"><i class="fas fa-plus-circle"></i> إضافة اختبار جديد</a>
        <a href="results.php?action=add" class="btn btn-primary"><i class="fas fa-plus-circle"></i> إدخال نتيجة جديدة</a>
        <a href="reports.php" class="btn btn-secondary"><i class="fas fa-chart-bar"></i> التقارير</a>
    </div>
</div>

<!-- Recent Results -->
<div class="table-container">
    <h2 class="table-title">أحدث النتائج</h2>
    
    <?php if (count($recentResults) > 0): ?>
    <table class="data-table">
        <thead>
            <tr>
                <th data-sortable="true">الطالب</th>
                <th data-sortable="true">المادة</th>
                <th data-sortable="true">الدرجة</th>
                <th data-sortable="true">التقدير</th>
                <th data-sortable="true">تاريخ الإضافة</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($recentResults as $result): ?>
            <tr>
                <td><?php echo $result['student_name']; ?></td>
                <td><?php echo $result['subject_name']; ?></td>
                <td><?php echo $result['mark']; ?></td>
                <td>
                    <?php 
                    if ($result['mark'] >= 90) echo 'ممتاز';
                    elseif ($result['mark'] >= 80) echo 'جيد جداً';
                    elseif ($result['mark'] >= 70) echo 'جيد';
                    elseif ($result['mark'] >= 60) echo 'مقبول';
                    elseif ($result['mark'] >= 50) echo 'ضعيف';
                    else echo 'راسب';
                    ?>
                </td>
                <td><?php echo date('Y-m-d', strtotime($result['created_at'])); ?></td>
                <td class="action-buttons">
                    <a href="results.php?action=edit&id=<?php echo $result['id']; ?>" class="btn btn-secondary btn-sm"><i class="fas fa-edit"></i></a>
                    <a href="results.php?action=delete&id=<?php echo $result['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirmDelete(event);"><i class="fas fa-trash"></i></a>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
    <?php else: ?>
    <p class="no-data">لا توجد نتائج حتى الآن</p>
    <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?> 