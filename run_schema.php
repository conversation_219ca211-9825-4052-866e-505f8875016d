<?php
// معلومات الاتصال بقاعدة البيانات
$servername = "127.0.0.1";
$username = "root";
$password = "";
$dbname = "ruwaq_db";

// إنشاء الاتصال
try {
    $conn = new mysqli($servername, $username, $password, $dbname);
    // التحقق من الاتصال
    if ($conn->connect_error) {
        die("فشل الاتصال: " . $conn->connect_error);
    }

    // قراءة ملف schema.sql
    $schema_file = 'database/schema.sql';
    if (file_exists($schema_file)) {
        $sql = file_get_contents($schema_file);
        
        // تنفيذ الاستعلامات متعددة
        $result = $conn->multi_query($sql);
        
        if ($result) {
            echo "تم تهيئة هيكل قاعدة البيانات بنجاح.<br>";
            echo "<a href='update_database.php' class='btn btn-primary'>تطبيق التحديثات الجديدة</a><br>";
            echo "<a href='index.php' class='btn btn-success'>الذهاب إلى الصفحة الرئيسية</a>";
        } else {
            echo "خطأ في تنفيذ السكريبت: " . $conn->error;
        }
    } else {
        echo "ملف schema.sql غير موجود!";
    }

    $conn->close();
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تنفيذ هيكل قاعدة البيانات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 20px;
            text-align: center;
            background-color: #f8f9fa;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0056b3;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            text-decoration: none;
            color: white;
            font-weight: bold;
        }
        .btn-primary {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>تنفيذ هيكل قاعدة البيانات</h1>
        <p>هذه الصفحة تقوم بتنفيذ سكريبت إنشاء الجداول اللازمة للنظام.</p>
    </div>
</body>
</html> 