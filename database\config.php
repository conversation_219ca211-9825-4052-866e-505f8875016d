<?php
// Database connection configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'ruwaq_db');
define('DB_CHARSET', 'utf8mb4');

// ======= إضافة وضع المعاينة =========
// تحقق مما إذا كنا في وضع المعاينة
define('PREVIEW_MODE', false);

// Create connection - only try if not in preview mode
function connectDB() {
    global $mock_data;
    
    // إذا كنا في وضع المعاينة، قم بإرجاع كائن وهمي بدلاً من الاتصال بقاعدة البيانات
    if (PREVIEW_MODE) {
        return new MockDatabase();
    }
    
    try {
        $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
        
        // Check connection
        if ($conn->connect_error) {
            die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
        }
        
        // Set character set
        $conn->set_charset(DB_CHARSET);
        
        return $conn;
    } catch (Exception $e) {
        die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
    }
}

// Mock Database Class for Preview Mode
class MockDatabase {
    public $mock_data = [
        'stages' => [
            ['id' => 1, 'name' => 'تمهيدية', 'description' => 'المرحلة التمهيدية'],
            ['id' => 2, 'name' => 'متوسطة', 'description' => 'المرحلة المتوسطة'],
            ['id' => 3, 'name' => 'تخصصية', 'description' => 'المرحلة التخصصية'],
        ],
        'levels' => [
            ['id' => 1, 'name' => 'المستوى الأول', 'stage_id' => 1],
            ['id' => 2, 'name' => 'المستوى الثاني', 'stage_id' => 1],
            ['id' => 3, 'name' => 'المستوى الأول', 'stage_id' => 2],
            ['id' => 4, 'name' => 'المستوى الثاني', 'stage_id' => 2],
            ['id' => 5, 'name' => 'المستوى الأول', 'stage_id' => 3],
            ['id' => 6, 'name' => 'المستوى الثاني', 'stage_id' => 3],
        ],
        'specializations' => [
            ['id' => 1, 'name' => 'فقه', 'description' => 'تخصص الفقه'],
            ['id' => 2, 'name' => 'تفسير وحديث', 'description' => 'تخصص التفسير والحديث'],
            ['id' => 3, 'name' => 'عقيدة', 'description' => 'تخصص العقيدة'],
            ['id' => 4, 'name' => 'لغة عربية', 'description' => 'تخصص اللغة العربية'],
        ],
        'students' => [
            [
                'id' => 1, 
                'user_id' => 2, 
                'student_id' => 'ST0001', 
                'full_name' => 'أحمد محمد علي',
                'national_id' => '30010234567891',
                'phone_number' => '01012345678',
                'previous_seat_number' => '',
                'current_seat_number' => 'S001',
                'stage_id' => 1, 
                'level_id' => 1, 
                'specialization_id' => null,
                'juristic_school' => 'حنفي',
                'student_status' => 'مستجد',
                'status' => 'active',
                'academic_year' => '2024-2025',
                'enrollment_date' => '2024-01-15',
                'stage_name' => 'تمهيدية',
                'level_name' => 'المستوى الأول',
                'specialization_name' => null
            ],
            [
                'id' => 2, 
                'user_id' => 3, 
                'student_id' => 'ST0002', 
                'full_name' => 'محمود أحمد سعيد',
                'national_id' => '30010234567892',
                'phone_number' => '01112345678',
                'previous_seat_number' => 'S002',
                'current_seat_number' => 'S003',
                'stage_id' => 2, 
                'level_id' => 3, 
                'specialization_id' => null,
                'juristic_school' => 'شافعي',
                'student_status' => 'معيد',
                'status' => 'active',
                'academic_year' => '2024-2025',
                'enrollment_date' => '2024-01-20',
                'stage_name' => 'متوسطة',
                'level_name' => 'المستوى الأول',
                'specialization_name' => null
            ],
            [
                'id' => 3, 
                'user_id' => 4, 
                'student_id' => 'ST0003', 
                'full_name' => 'خالد عبدالله محمد',
                'national_id' => '30010234567893',
                'phone_number' => '01212345678',
                'previous_seat_number' => '',
                'current_seat_number' => 'S004',
                'stage_id' => 3, 
                'level_id' => 5, 
                'specialization_id' => 1,
                'juristic_school' => 'مالكي',
                'student_status' => 'مستجد',
                'status' => 'active',
                'academic_year' => '2024-2025',
                'enrollment_date' => '2024-01-25',
                'stage_name' => 'تخصصية',
                'level_name' => 'المستوى الأول',
                'specialization_name' => 'فقه'
            ],
        ]
    ];
    
    // Mock prepare method
    public function prepare($sql) {
        return new MockStatement($sql, $this->mock_data);
    }
    
    // Mock query method
    public function query($sql) {
        return new MockResult($sql, $this->mock_data);
    }
    
    // Mock close method
    public function close() {
        return true;
    }
    
    // Mock property access
    public function __get($name) {
        if ($name === 'insert_id') {
            return 1;
        }
        return null;
    }
}

// Mock Statement Class
class MockStatement {
    private $sql;
    private $data;
    private $params = [];
    private $types = '';
    private $result = null;
    
    public function __construct($sql, $data) {
        $this->sql = $sql;
        $this->data = $data;
    }
    
    public function bind_param($types, ...$params) {
        $this->types = $types;
        $this->params = $params;
        return true;
    }
    
    public function execute() {
        return true;
    }
    
    public function get_result() {
        if (strpos($this->sql, 'FROM stages') !== false) {
            return new MockResult('stages', $this->data);
        } elseif (strpos($this->sql, 'FROM levels') !== false) {
            return new MockResult('levels', $this->data);
        } elseif (strpos($this->sql, 'FROM specializations') !== false) {
            return new MockResult('specializations', $this->data);
        } elseif (strpos($this->sql, 'FROM students') !== false) {
            return new MockResult('students', $this->data);
        }
        return new MockResult('', $this->data);
    }
    
    public function close() {
        return true;
    }
    
    public function __get($name) {
        if ($name === 'affected_rows') {
            return 1;
        }
        return null;
    }
}

// Mock Result Class
class MockResult {
    private $type;
    private $data;
    private $index = 0;
    
    public function __construct($type, $data) {
        $this->type = $type;
        $this->data = $data;
    }
    
    public function fetch_assoc() {
        if (!isset($this->data[$this->type]) || $this->index >= count($this->data[$this->type])) {
            return null;
        }
        return $this->data[$this->type][$this->index++];
    }
    
    public function fetch_all($mode) {
        return $this->data[$this->type] ?? [];
    }
    
    public function __get($name) {
        if ($name === 'num_rows') {
            return count($this->data[$this->type] ?? []);
        }
        return null;
    }
}

// Close connection
function closeDB($conn) {
    if (!PREVIEW_MODE) {
        $conn->close();
    }
    return true;
}

// Execute query and return result
function executeQuery($sql, $params = []) {
    $conn = connectDB();
    
    if (PREVIEW_MODE) {
        // في وضع المعاينة، نستخدم الكائن الوهمي مباشرة
        $result = $conn->query($sql);
        return $result;
    }
    
    $stmt = $conn->prepare($sql);
    
    if ($stmt === false) {
        die("خطأ في الاستعلام: " . $conn->error);
    }
    
    if (!empty($params)) {
        $types = '';
        $bindParams = [];
        
        foreach ($params as $param) {
            if (is_int($param)) {
                $types .= 'i'; // integer
            } elseif (is_double($param) || is_float($param)) {
                $types .= 'd'; // double
            } elseif (is_string($param)) {
                $types .= 's'; // string
            } else {
                $types .= 'b'; // blob
            }
            $bindParams[] = $param;
        }
        
        array_unshift($bindParams, $types);
        call_user_func_array([$stmt, 'bind_param'], refValues($bindParams));
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    $stmt->close();
    closeDB($conn);
    
    return $result;
}

// Helper function for reference parameters
function refValues($arr) {
    $refs = [];
    foreach ($arr as $key => $value) {
        $refs[$key] = &$arr[$key];
    }
    return $refs;
}

// Get single row
function getRow($sql, $params = []) {
    $result = executeQuery($sql, $params);
    return $result->fetch_assoc();
}

// Get multiple rows
function getRows($sql, $params = []) {
    $result = executeQuery($sql, $params);
    return PREVIEW_MODE ? $result->fetch_all(0) : $result->fetch_all(MYSQLI_ASSOC);
}

// Insert data and return the last insert ID
function insertData($sql, $params = []) {
    $conn = connectDB();
    
    if (PREVIEW_MODE) {
        return 1; // في وضع المعاينة، نقوم بإرجاع رقم 1 فقط
    }
    
    $stmt = $conn->prepare($sql);
    
    if ($stmt === false) {
        die("خطأ في الاستعلام: " . $conn->error);
    }
    
    if (!empty($params)) {
        $types = '';
        $bindParams = [];
        
        foreach ($params as $param) {
            if (is_int($param)) {
                $types .= 'i';
            } elseif (is_double($param) || is_float($param)) {
                $types .= 'd';
            } elseif (is_string($param)) {
                $types .= 's';
            } else {
                $types .= 'b';
            }
            $bindParams[] = $param;
        }
        
        array_unshift($bindParams, $types);
        call_user_func_array([$stmt, 'bind_param'], refValues($bindParams));
    }
    
    $stmt->execute();
    $insertId = $conn->insert_id;
    $stmt->close();
    closeDB($conn);
    
    return $insertId;
}

// Update data and return the number of affected rows
function updateData($sql, $params = []) {
    $conn = connectDB();
    
    if (PREVIEW_MODE) {
        return 1; // في وضع المعاينة، نقوم بإرجاع رقم 1 فقط
    }
    
    $stmt = $conn->prepare($sql);
    
    if ($stmt === false) {
        die("خطأ في الاستعلام: " . $conn->error);
    }
    
    if (!empty($params)) {
        $types = '';
        $bindParams = [];
        
        foreach ($params as $param) {
            if (is_int($param)) {
                $types .= 'i';
            } elseif (is_double($param) || is_float($param)) {
                $types .= 'd';
            } elseif (is_string($param)) {
                $types .= 's';
            } else {
                $types .= 'b';
            }
            $bindParams[] = $param;
        }
        
        array_unshift($bindParams, $types);
        call_user_func_array([$stmt, 'bind_param'], refValues($bindParams));
    }
    
    $stmt->execute();
    $affectedRows = $stmt->affected_rows;
    $stmt->close();
    closeDB($conn);
    
    return $affectedRows;
}

// نستخدم دالة sanitizeInput من ملف functions.php
// function sanitizeInput($data) {
//     $data = trim($data);
//     $data = stripslashes($data);
//     $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
//     return $data;
// }
?>