/* Main CSS file for Ruwaq Al-Uloom Al-Shariah wal-Arabia Student Management System */

/* Set RTL direction and base font */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html, body {
    direction: rtl;
    font-family: 'Cairo', '<PERSON><PERSON><PERSON>', sans-serif;
    font-size: 16px;
    color: #333;
    background-color: #f5f5f5;
    height: 100%;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Container */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Navbar */
.navbar {
    background-color: #006c35;
    color: #fff;
    padding: 1rem 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.navbar-logo {
    display: flex;
    align-items: center;
}

.navbar-logo img {
    height: 50px;
    margin-left: 10px;
}

.navbar-logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-menu {
    display: flex;
    list-style: none;
}

.navbar-menu li {
    margin-right: 20px;
}

.navbar-menu li:last-child {
    margin-right: 0;
}

.navbar-menu a {
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-menu a:hover {
    color: #ffcc00;
}

.navbar-toggle {
    display: none;
    cursor: pointer;
    font-size: 1.5rem;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 2rem 0;
}

/* Dashboard Sections */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.stat-card h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: #006c35;
}

.stat-card .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
}

/* Tables */
.table-container {
    overflow-x: auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 30px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th, .data-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid #e0e0e0;
}

.data-table th {
    background-color: #f8f8f8;
    font-weight: 600;
    color: #333;
}

.data-table tr:hover {
    background-color: #f5f5f5;
}

.data-table .action-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
}

/* Forms */
.form-container {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 30px;
}

.form-title {
    font-size: 1.5rem;
    margin-bottom: 20px;
    color: #006c35;
    text-align: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #006c35;
    outline: none;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -10px;
    margin-left: -10px;
}

.form-col {
    flex: 1;
    padding: 0 10px;
    min-width: 250px;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #006c35;
    color: #fff;
}

.btn-primary:hover {
    background-color: #005228;
}

.btn-secondary {
    background-color: #6c757d;
    color: #fff;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-danger {
    background-color: #dc3545;
    color: #fff;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-success {
    background-color: #28a745;
    color: #fff;
}

.btn-success:hover {
    background-color: #218838;
}

/* Alerts */
.alert {
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeeba;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Footer */
.footer {
    background-color: #2c3e50;
    color: #fff;
    padding: 1.5rem 0;
    text-align: center;
}

.footer p {
    margin: 0;
}

/* Login Page */
.login-container {
    max-width: 400px;
    margin: 100px auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 30px;
}

.login-logo {
    text-align: center;
    margin-bottom: 30px;
}

.login-logo img {
    height: 80px;
}

.login-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.8rem;
    color: #006c35;
}

/* Responsive styles */
@media (max-width: 768px) {
    .navbar-menu {
        display: none;
        position: absolute;
        top: 70px;
        right: 0;
        width: 100%;
        background-color: #006c35;
        flex-direction: column;
        padding: 20px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .navbar-menu.active {
        display: flex;
    }
    
    .navbar-menu li {
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .navbar-toggle {
        display: block;
    }
    
    .form-col {
        flex: 100%;
        margin-bottom: 15px;
    }
}

/* Print styles */
@media print {
    .navbar, .footer, .no-print {
        display: none;
    }
    
    body {
        background-color: #fff;
    }
    
    .container {
        width: 100%;
        max-width: 100%;
        padding: 0;
    }
    
    .main-content {
        padding: 0;
    }
    
    .table-container,
    .form-container {
        box-shadow: none;
        border: 1px solid #ddd;
    }
} 