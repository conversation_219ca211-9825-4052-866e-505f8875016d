// Main JavaScript file for Ruwaq <PERSON>-Uloom Al-Shariah wal-Arabia Student Management System

document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const navbarToggle = document.querySelector('.navbar-toggle');
    const navbarMenu = document.querySelector('.navbar-menu');
    
    if (navbarToggle && navbarMenu) {
        navbarToggle.addEventListener('click', function() {
            navbarMenu.classList.toggle('active');
        });
    }
    
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    if (alerts.length > 0) {
        alerts.forEach(alert => {
            setTimeout(() => {
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 500);
            }, 5000);
        });
    }
    
    // Form validation
    const forms = document.querySelectorAll('form.needs-validation');
    if (forms.length > 0) {
        forms.forEach(form => {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }
    
    // Data tables initialization
    initializeDataTables();
    
    // Initialize select dropdowns with search
    initializeSelectDropdowns();
    
    // Initialize student progression
    initializeStudentProgression();
});

// Function to initialize data tables
function initializeDataTables() {
    const dataTables = document.querySelectorAll('.data-table');
    if (dataTables.length > 0) {
        dataTables.forEach(table => {
            // Check if the search input exists for this table
            const tableContainer = table.closest('.table-container');
            const searchInput = tableContainer.querySelector('.table-search-input');
            
            if (searchInput) {
                searchInput.addEventListener('keyup', function() {
                    const searchText = this.value.toLowerCase();
                    const rows = table.querySelectorAll('tbody tr');
                    
                    rows.forEach(row => {
                        const text = row.textContent.toLowerCase();
                        if (text.indexOf(searchText) > -1) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    });
                });
            }
            
            // Add sort functionality
            const headers = table.querySelectorAll('th[data-sortable="true"]');
            headers.forEach(header => {
                header.addEventListener('click', function() {
                    const columnIndex = Array.from(header.parentNode.children).indexOf(header);
                    const isAscending = header.classList.contains('sort-asc');
                    
                    // Remove sort classes from all headers
                    headers.forEach(h => {
                        h.classList.remove('sort-asc', 'sort-desc');
                    });
                    
                    // Set sort class for the clicked header
                    header.classList.add(isAscending ? 'sort-desc' : 'sort-asc');
                    
                    // Sort the table
                    sortTable(table, columnIndex, !isAscending);
                });
            });
        });
    }
}

// Function to sort a table
function sortTable(table, columnIndex, ascending) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    // Sort the rows
    rows.sort((a, b) => {
        const aValue = a.children[columnIndex].textContent.trim();
        const bValue = b.children[columnIndex].textContent.trim();
        
        if (!isNaN(aValue) && !isNaN(bValue)) {
            return ascending ? aValue - bValue : bValue - aValue;
        } else {
            return ascending ? aValue.localeCompare(bValue, 'ar') : bValue.localeCompare(aValue, 'ar');
        }
    });
    
    // Clear the table body
    while (tbody.firstChild) {
        tbody.removeChild(tbody.firstChild);
    }
    
    // Add the sorted rows
    rows.forEach(row => {
        tbody.appendChild(row);
    });
}

// Function to initialize select dropdowns with search
function initializeSelectDropdowns() {
    // This function can be extended with a custom select implementation
    // or integrated with a library like Select2 if needed
}

// Grade calculation logic
function calculateGrade(mark) {
    if (mark >= 90) {
        return 'ممتاز';
    } else if (mark >= 80) {
        return 'جيد جداً';
    } else if (mark >= 70) {
        return 'جيد';
    } else if (mark >= 60) {
        return 'مقبول';
    } else if (mark >= 50) {
        return 'ضعيف';
    } else {
        return 'راسب';
    }
}

// Function to determine if a student passes or fails
function determineStatus(subjectResults) {
    const failedSubjects = subjectResults.filter(subject => subject.mark < 50);
    
    if (failedSubjects.length === 0) {
        return 'ناجح'; // All subjects passed
    } else if (failedSubjects.length <= 2) {
        return 'منقول بمواد'; // Failed 1 or 2 subjects
    } else {
        return 'باقي للإعادة'; // Failed more than 2 subjects
    }
}

// Student progression initialization
function initializeStudentProgression() {
    const progressionForm = document.getElementById('progressionForm');
    
    if (progressionForm) {
        progressionForm.addEventListener('submit', function(event) {
            event.preventDefault();
            
            // Get all student marks
            const subjectRows = document.querySelectorAll('.subject-row');
            const subjectResults = [];
            
            subjectRows.forEach(row => {
                const subjectId = row.getAttribute('data-subject-id');
                const markInput = row.querySelector('.subject-mark');
                const mark = parseFloat(markInput.value) || 0;
                
                subjectResults.push({
                    subjectId,
                    mark
                });
            });
            
            // Determine student status
            const status = determineStatus(subjectResults);
            document.getElementById('studentStatus').value = status;
            
            // Now form can be submitted
            progressionForm.submit();
        });
    }
    
    // Result details toggle
    const toggleButtons = document.querySelectorAll('.toggle-results');
    if (toggleButtons.length > 0) {
        toggleButtons.forEach(button => {
            button.addEventListener('click', function() {
                const resultId = this.getAttribute('data-result-id');
                const resultDetails = document.getElementById('result-details-' + resultId);
                
                if (resultDetails) {
                    resultDetails.classList.toggle('hidden');
                    this.textContent = resultDetails.classList.contains('hidden') ? 'عرض التفاصيل' : 'إخفاء التفاصيل';
                }
            });
        });
    }
}

// Print function
function printContent(elementId) {
    const content = document.getElementById(elementId);
    if (!content) return;
    
    const originalContents = document.body.innerHTML;
    const printContents = content.innerHTML;
    
    document.body.innerHTML = `
        <div dir="rtl">
            ${printContents}
        </div>
    `;
    
    window.print();
    document.body.innerHTML = originalContents;
    
    // Reinitialize event listeners
    document.addEventListener('DOMContentLoaded', function() {
        // Re-initialize all event listeners
        initializeDataTables();
        initializeSelectDropdowns();
        initializeStudentProgression();
    });
}

// Confirm delete
function confirmDelete(event, message) {
    if (!confirm(message || 'هل أنت متأكد من عملية الحذف؟')) {
        event.preventDefault();
        return false;
    }
    return true;
} 