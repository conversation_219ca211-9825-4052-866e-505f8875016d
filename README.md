# نظام إدارة طلاب رواق العلوم الشرعية والعربية بالأزهر الشريف

## نظرة عامة

هذا النظام تم تطويره لإدارة طلاب رواق العلوم الشرعية والعربية بالأزهر الشريف. يوفر النظام إمكانية إدارة الطلاب من جميع النواحي بما في ذلك الاختبارات والنتائج والتصعيد للمستوى والمرحلة الأعلى.

## المميزات الرئيسية

- إدارة بيانات الطلاب (إضافة، تعديل، حذف)
- إدارة الاختبارات
- إدارة نتائج الطلاب
- نظام التصعيد الآلي للمستوى والمرحلة الأعلى بناءً على النتائج
- تقارير متنوعة للطلاب والنتائج

## متطلبات النظام

- PHP 7.4 أو أعلى
- MySQL 5.7 أو أعلى
- خادم ويب (Apache, Nginx)

## طريقة الإعداد

1. قم بتنزيل ملفات المشروع إلى مجلد خادم الويب الخاص بك.
2. قم بإنشاء قاعدة بيانات MySQL جديدة.
3. قم بتعديل ملف `database/config.php` بمعلومات الاتصال بقاعدة البيانات الخاصة بك.
4. قم بتشغيل البرنامج النصي SQL الموجود في `database/schema.sql` لإنشاء الجداول المطلوبة.
5. قم بالوصول إلى النظام عبر المتصفح.

## معلومات تسجيل الدخول

- اسم المستخدم: admin
- كلمة المرور: admin

## هيكل المراحل التعليمية

النظام يدعم الهيكل التعليمي التالي:

### المراحل
- تمهيدية
  * مستوى أول
  * مستوى ثان
- متوسطة
  * مستوى أول
  * مستوى ثان
- تخصصية (4 تخصصات)
  * فقه (4 مستويات)
  * تفسير وحديث (4 مستويات)
  * عقيدة (4 مستويات)
  * لغة عربية (4 مستويات)

## قواعد النجاح والرسوب

- الدرجة النهائية لكل مادة: 100 درجة
- درجة النجاح: 50 درجة فأكثر
- يحق للطالب الراسب في مادتين أو مادة واحدة التصعيد للمستوى الأعلى (منقول بمواد)
- في حالة رسوب الطالب في أكثر من مادتين، يبقى في نفس المستوى (باقي للإعادة)
- في حالة نجاح الطالب في جميع المواد، يتم تصعيده للمستوى التالي (ناجح)

## هيكل المجلدات

- `/css`: ملفات CSS
- `/js`: ملفات JavaScript
- `/database`: ملفات قاعدة البيانات
- `/includes`: ملفات PHP المشتركة
- `/views`: نماذج العرض
- `/assets`: الصور والملفات الثابتة الأخرى

## لمزيد من المعلومات

للمزيد من المعلومات حول مواد كل مرحلة ومستوى، يرجى زيارة:
[موقع الأزهر الشريف - رواق العلوم الشرعية والعربية](https://www.azhar.eg/alruwaq/oloom-2.htm)

## تطوير

- HTML, CSS, JS
- PHP, MySQL

## الترخيص

تم تطوير هذا النظام خصيصًا لرواق العلوم الشرعية والعربية بالأزهر الشريف. جميع الحقوق محفوظة. 