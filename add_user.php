<?php
// Include database configuration
require_once 'includes/functions.php';

// تأكد من أن هذا الملف يمكن تشغيله فقط من قبل المسؤول
// في الإنتاج، يجب إضافة المزيد من التحقق من الصلاحيات

// بيانات المستخدم الجديد
$username = 'admin'; // اسم المستخدم
$password = 'admin123'; // كلمة المرور
$full_name = 'مدير النظام'; // الاسم الكامل
$role = 'admin'; // الدور (admin, teacher, student)

// تشفير كلمة المرور
$hashed_password = password_hash($password, PASSWORD_DEFAULT);

// التحقق من وجود المستخدم
$sql = "SELECT * FROM users WHERE username = ?";
$existing_user = getRow($sql, [$username]);

if ($existing_user) {
    echo "المستخدم موجود بالفعل!";
} else {
    // إضافة المستخدم الجديد
    $sql = "INSERT INTO users (username, password, full_name, role) VALUES (?, ?, ?, ?)";
    $params = [$username, $hashed_password, $full_name, $role];
    
    $user_id = insertData($sql, $params);
    
    if ($user_id) {
        echo "تم إضافة المستخدم بنجاح! معرف المستخدم: " . $user_id;
    } else {
        echo "فشل في إضافة المستخدم!";
    }
}

// عرض معلومات حول إعدادات قاعدة البيانات
echo "<hr>";
echo "<h3>معلومات إعدادات قاعدة البيانات:</h3>";
echo "<p>اسم المضيف: " . DB_HOST . "</p>";
echo "<p>اسم المستخدم: " . DB_USER . "</p>";
echo "<p>اسم قاعدة البيانات: " . DB_NAME . "</p>";
echo "<p>وضع المعاينة: " . (PREVIEW_MODE ? 'مفعل' : 'معطل') . "</p>";

// التحقق من الاتصال بقاعدة البيانات
echo "<hr>";
echo "<h3>اختبار الاتصال بقاعدة البيانات:</h3>";
try {
    $conn = connectDB();
    if ($conn instanceof mysqli) {
        echo "<p style='color:green'>تم الاتصال بقاعدة البيانات بنجاح!</p>";
    } elseif ($conn instanceof MockDatabase) {
        echo "<p style='color:orange'>تم إنشاء كائن قاعدة بيانات وهمية (وضع المعاينة مفعل)!</p>";
    } else {
        echo "<p style='color:red'>فشل الاتصال بقاعدة البيانات!</p>";
    }
} catch (Exception $e) {
    echo "<p style='color:red'>خطأ: " . $e->getMessage() . "</p>";
}
?>