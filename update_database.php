<?php
/**
 * Database Update Script for Student Management System
 * This script updates the database schema to support the new required fields for students.
 */

// Include database configuration
require_once 'database/config.php';

// Connect to the database
$conn = connectDB();

// Begin transaction
$conn->begin_transaction();

try {
    // Read SQL update script
    $sql_script = file_get_contents('database/update_schema.sql');
    
    // Split script into separate queries
    $queries = explode(';', $sql_script);
    
    // Execute each query
    foreach ($queries as $query) {
        $query = trim($query);
        if (!empty($query)) {
            if (!$conn->query($query)) {
                throw new Exception("Error executing query: " . $conn->error);
            }
        }
    }
    
    // Add full_name to students table if it doesn't exist
    $result = $conn->query("SHOW COLUMNS FROM students LIKE 'full_name'");
    if ($result->num_rows == 0) {
        $conn->query("ALTER TABLE students ADD COLUMN full_name VARCHAR(100) NOT NULL AFTER student_id");
        
        // Copy full_name from users table for existing records
        $conn->query("UPDATE students s JOIN users u ON s.user_id = u.id SET s.full_name = u.full_name");
    }
    
    // Commit transaction
    $conn->commit();
    echo "Database update completed successfully!";
} catch (Exception $e) {
    // Rollback transaction
    $conn->rollback();
    echo "Database update failed: " . $e->getMessage();
}

// Close connection
closeDB($conn);
?> 