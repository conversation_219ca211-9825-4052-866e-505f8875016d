<?php
// معلومات الاتصال بقاعدة البيانات
$servername = "127.0.0.1";
$username = "root";
$password = "";

// إنشاء الاتصال
try {
    $conn = new mysqli($servername, $username, $password);
    // التحقق من الاتصال
    if ($conn->connect_error) {
        die("فشل الاتصال: " . $conn->connect_error);
    }

    // إنشاء قاعدة البيانات إذا لم تكن موجودة
    $sql = "CREATE DATABASE IF NOT EXISTS ruwaq_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    if ($conn->query($sql) === TRUE) {
        echo "تم إنشاء قاعدة البيانات بنجاح أو هي موجودة بالفعل<br>";
        
        // اختيار قاعدة البيانات
        $conn->select_db("ruwaq_db");
        
        // التحقق مما إذا كانت الجداول الرئيسية موجودة
        $result = $conn->query("SHOW TABLES LIKE 'students'");
        if ($result->num_rows == 0) {
            // إضافة رابط إلى ملف schema.sql
            echo "يبدو أن الجداول غير موجودة. يجب تشغيل ملف schema.sql.<br>";
            echo "<a href='run_schema.php' class='btn btn-primary'>تثبيت هيكل قاعدة البيانات</a>";
        } else {
            echo "الجداول موجودة بالفعل.<br>";
            echo "<a href='index.php' class='btn btn-success'>الذهاب إلى الصفحة الرئيسية</a>";
        }
    } else {
        echo "خطأ في إنشاء قاعدة البيانات: " . $conn->error;
    }

    $conn->close();
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء قاعدة البيانات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 20px;
            text-align: center;
            background-color: #f8f9fa;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0056b3;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            text-decoration: none;
            color: white;
            font-weight: bold;
        }
        .btn-primary {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>تهيئة قاعدة البيانات</h1>
        <p>هذه الصفحة تقوم بإنشاء قاعدة البيانات اللازمة للنظام.</p>
        <p>إذا كنت ترى أي أخطاء، تأكد من تشغيل خادم MySQL وأن المستخدم 'root' لديه الصلاحيات المناسبة.</p>
    </div>
</body>
</html> 