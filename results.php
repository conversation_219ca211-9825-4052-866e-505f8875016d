<?php
require_once 'includes/functions.php';
include 'includes/header.php';

// Get action from query string
$action = isset($_GET['action']) ? $_GET['action'] : 'list';
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$exam_id = isset($_GET['exam_id']) ? intval($_GET['exam_id']) : 0;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add' || $action === 'edit') {
        // Get form data
        $student_id = intval($_POST['student_id']);
        $exam_id = intval($_POST['exam_id']);
        $subject_id = intval($_POST['subject_id']);
        $mark = floatval($_POST['mark']);
        
        // Validate data
        $errors = [];
        if ($student_id <= 0) {
            $errors[] = 'يجب اختيار الطالب';
        }
        if ($exam_id <= 0) {
            $errors[] = 'يجب اختيار الاختبار';
        }
        if ($subject_id <= 0) {
            $errors[] = 'يجب اختيار المادة';
        }
        if ($mark < 0 || $mark > 100) {
            $errors[] = 'يجب أن تكون الدرجة بين 0 و 100';
        }
        
        if (empty($errors)) {
            $conn = connectDB();
            
            // For adding a new result
            if ($action === 'add') {
                // Check if result already exists
                $sql = "SELECT * FROM exam_results WHERE student_id = ? AND exam_id = ? AND subject_id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param('iii', $student_id, $exam_id, $subject_id);
                $stmt->execute();
                $result = $stmt->get_result();
                $existingResult = $result->fetch_assoc();
                $stmt->close();
                
                if ($existingResult) {
                    $_SESSION['error_message'] = 'النتيجة موجودة بالفعل لهذا الطالب في هذا الاختبار';
                } else {
                    $sql = "INSERT INTO exam_results (student_id, exam_id, subject_id, mark) 
                           VALUES (?, ?, ?, ?)";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param('iiid', $student_id, $exam_id, $subject_id, $mark);
                    $stmt->execute();
                    $stmt->close();
                    
                    $_SESSION['success_message'] = 'تمت إضافة النتيجة بنجاح';
                    header('Location: results.php');
                    exit;
                }
            } 
            // For updating an existing result
            else if ($action === 'edit' && $id > 0) {
                $sql = "UPDATE exam_results SET student_id = ?, exam_id = ?, subject_id = ?, mark = ? 
                       WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param('iiidd', $student_id, $exam_id, $subject_id, $mark, $id);
                $stmt->execute();
                $stmt->close();
                
                $_SESSION['success_message'] = 'تم تحديث النتيجة بنجاح';
                header('Location: results.php');
                exit;
            }
            
            closeDB($conn);
        } else {
            $_SESSION['error_message'] = implode('<br>', $errors);
        }
    } 
    // Handle delete action
    else if ($action === 'delete' && $id > 0) {
        $conn = connectDB();
        
        // Delete the result record
        $sql = "DELETE FROM exam_results WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('i', $id);
        $stmt->execute();
        $stmt->close();
        
        closeDB($conn);
        
        $_SESSION['success_message'] = 'تم حذف النتيجة بنجاح';
        header('Location: results.php');
        exit;
    }
    // Handle batch add action
    else if ($action === 'batch_add' && $exam_id > 0) {
        $student_ids = isset($_POST['student_ids']) ? $_POST['student_ids'] : [];
        $marks = isset($_POST['marks']) ? $_POST['marks'] : [];
        $subject_id = intval($_POST['subject_id']);
        
        if (empty($student_ids) || empty($marks) || $subject_id <= 0) {
            $_SESSION['error_message'] = 'بيانات غير مكتملة';
        } else {
            $conn = connectDB();
            
            $successCount = 0;
            foreach ($student_ids as $index => $student_id) {
                if (isset($marks[$index]) && $marks[$index] >= 0 && $marks[$index] <= 100) {
                    // Check if result already exists
                    $sql = "SELECT * FROM exam_results WHERE student_id = ? AND exam_id = ? AND subject_id = ?";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param('iii', $student_id, $exam_id, $subject_id);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    $existingResult = $result->fetch_assoc();
                    $stmt->close();
                    
                    if ($existingResult) {
                        // Update existing result
                        $sql = "UPDATE exam_results SET mark = ? WHERE student_id = ? AND exam_id = ? AND subject_id = ?";
                        $stmt = $conn->prepare($sql);
                        $stmt->bind_param('diii', $marks[$index], $student_id, $exam_id, $subject_id);
                        $stmt->execute();
                        $stmt->close();
                    } else {
                        // Insert new result
                        $sql = "INSERT INTO exam_results (student_id, exam_id, subject_id, mark) VALUES (?, ?, ?, ?)";
                        $stmt = $conn->prepare($sql);
                        $stmt->bind_param('iiid', $student_id, $exam_id, $subject_id, $marks[$index]);
                        $stmt->execute();
                        $stmt->close();
                    }
                    
                    $successCount++;
                }
            }
            
            closeDB($conn);
            
            $_SESSION['success_message'] = "تم إضافة/تحديث $successCount نتيجة بنجاح";
            header('Location: results.php?exam_id=' . $exam_id);
            exit;
        }
    }
}

// Get data for display
$conn = connectDB();

// For edit action
$result = null;
if ($action === 'edit' && $id > 0) {
    $sql = "SELECT er.*, s.full_name as student_name, e.academic_year, e.semester, 
           sub.name as subject_name 
           FROM exam_results er 
           JOIN students st ON er.student_id = st.id 
           JOIN users s ON st.user_id = s.id 
           JOIN exams e ON er.exam_id = e.id 
           JOIN subjects sub ON er.subject_id = sub.id 
           WHERE er.id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $result = $stmt->get_result()->fetch_assoc();
    $stmt->close();
    
    if (!$result) {
        $_SESSION['error_message'] = 'النتيجة غير موجودة';
        header('Location: results.php');
        exit;
    }
}

// For list action
$results = [];
if ($action === 'list') {
    // Get filter values
    $filter_year = isset($_GET['academic_year']) ? sanitizeInput($_GET['academic_year']) : '';
    $filter_stage = isset($_GET['stage_id']) ? intval($_GET['stage_id']) : 0;
    $filter_level = isset($_GET['level_id']) ? intval($_GET['level_id']) : 0;
    
    // Build the query
    $sql = "SELECT er.*, s.full_name as student_name, st.student_id as student_code,
           e.academic_year, e.semester, sub.name as subject_name,
           stg.name as stage_name, l.name as level_name
           FROM exam_results er 
           JOIN students st ON er.student_id = st.id 
           JOIN users s ON st.user_id = s.id 
           JOIN exams e ON er.exam_id = e.id 
           JOIN subjects sub ON er.subject_id = sub.id
           JOIN stages stg ON st.stage_id = stg.id
           JOIN levels l ON st.level_id = l.id
           WHERE 1=1";
    
    $params = [];
    $types = '';
    
    if (!empty($filter_year)) {
        $sql .= " AND e.academic_year = ?";
        $params[] = $filter_year;
        $types .= 's';
    }
    
    if ($filter_stage > 0) {
        $sql .= " AND st.stage_id = ?";
        $params[] = $filter_stage;
        $types .= 'i';
    }
    
    if ($filter_level > 0) {
        $sql .= " AND st.level_id = ?";
        $params[] = $filter_level;
        $types .= 'i';
    }
    
    if ($exam_id > 0) {
        $sql .= " AND er.exam_id = ?";
        $params[] = $exam_id;
        $types .= 'i';
    }
    
    $sql .= " ORDER BY er.created_at DESC";
    
    $stmt = $conn->prepare($sql);
    
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        $results[] = $row;
    }
    
    $stmt->close();
}

// For batch add action (displaying a form for adding multiple results at once)
$examDetails = null;
$students = [];
if ($action === 'batch_add' && $exam_id > 0) {
    // Get exam details
    $sql = "SELECT e.*, s.name as subject_name, s.id as subject_id, 
           stg.id as stage_id, stg.name as stage_name, 
           l.id as level_id, l.name as level_name
           FROM exams e 
           JOIN subjects s ON e.subject_id = s.id 
           JOIN stages stg ON s.stage_id = stg.id 
           JOIN levels l ON s.level_id = l.id 
           WHERE e.id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $exam_id);
    $stmt->execute();
    $examDetails = $stmt->get_result()->fetch_assoc();
    $stmt->close();
    
    if (!$examDetails) {
        $_SESSION['error_message'] = 'الاختبار غير موجود';
        header('Location: exams.php');
        exit;
    }
    
    // Get students in this level/stage
    $sql = "SELECT s.*, u.full_name 
           FROM students s 
           JOIN users u ON s.user_id = u.id 
           WHERE s.stage_id = ? AND s.level_id = ? AND s.status = 'active'
           ORDER BY u.full_name";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('ii', $examDetails['stage_id'], $examDetails['level_id']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        // Check if student already has a result for this exam
        $sql = "SELECT * FROM exam_results WHERE student_id = ? AND exam_id = ? AND subject_id = ?";
        $stmt2 = $conn->prepare($sql);
        $stmt2->bind_param('iii', $row['id'], $exam_id, $examDetails['subject_id']);
        $stmt2->execute();
        $existingResult = $stmt2->get_result()->fetch_assoc();
        $stmt2->close();
        
        $row['existing_mark'] = $existingResult ? $existingResult['mark'] : null;
        $students[] = $row;
    }
    
    $stmt->close();
}

// Get all students for dropdown
$allStudents = [];
$sql = "SELECT s.id, s.student_id as code, u.full_name, st.name as stage, l.name as level
       FROM students s 
       JOIN users u ON s.user_id = u.id 
       JOIN stages st ON s.stage_id = st.id 
       JOIN levels l ON s.level_id = l.id 
       WHERE s.status = 'active'
       ORDER BY u.full_name";
$result = $conn->query($sql);
while ($row = $result->fetch_assoc()) {
    $allStudents[] = $row;
}

// Get all exams for dropdown
$allExams = [];
$sql = "SELECT e.id, e.exam_date, e.academic_year, e.semester, s.name as subject, 
       st.name as stage, l.name as level
       FROM exams e 
       JOIN subjects s ON e.subject_id = s.id 
       JOIN stages st ON s.stage_id = st.id 
       JOIN levels l ON s.level_id = l.id 
       ORDER BY e.exam_date DESC";
$result = $conn->query($sql);
while ($row = $result->fetch_assoc()) {
    $allExams[] = $row;
}

// Get all subjects for dropdown
$allSubjects = [];
$sql = "SELECT s.id, s.name, st.name as stage, l.name as level
       FROM subjects s 
       JOIN stages st ON s.stage_id = st.id 
       JOIN levels l ON s.level_id = l.id 
       ORDER BY s.name";
$result = $conn->query($sql);
while ($row = $result->fetch_assoc()) {
    $allSubjects[] = $row;
}

// Get all stages for filter
$stages = getAllStages();

// Get levels for the selected stage (if any)
$levels = [];
if (isset($_GET['stage_id']) && $_GET['stage_id'] > 0) {
    $levels = getLevelsByStage($_GET['stage_id']);
}

// Get all academic years for filter
$academicYears = getAcademicYears();

closeDB($conn);
?>

<?php if ($action === 'list'): ?>
<!-- Results List -->
<div class="page-header">
    <h1 class="page-title">إدارة النتائج</h1>
    <div class="page-actions">
        <a href="results.php?action=add" class="btn btn-primary"><i class="fas fa-plus-circle"></i> إضافة نتيجة جديدة</a>
    </div>
</div>

<!-- Filters -->
<div class="form-container">
    <h2 class="form-title">تصفية النتائج</h2>
    <form action="results.php" method="get" class="filter-form">
        <div class="form-row">
            <div class="form-col">
                <label for="academic_year" class="form-label">العام الدراسي</label>
                <select name="academic_year" id="academic_year" class="form-control" onchange="this.form.submit()">
                    <option value="">الكل</option>
                    <?php foreach ($academicYears as $year): ?>
                    <option value="<?php echo $year; ?>" <?php echo ($filter_year == $year) ? 'selected' : ''; ?>>
                        <?php echo $year; ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-col">
                <label for="stage_id" class="form-label">المرحلة</label>
                <select name="stage_id" id="stage_id" class="form-control" onchange="this.form.submit()">
                    <option value="0">الكل</option>
                    <?php foreach ($stages as $stage): ?>
                    <option value="<?php echo $stage['id']; ?>" <?php echo ($filter_stage == $stage['id']) ? 'selected' : ''; ?>>
                        <?php echo $stage['name']; ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-col">
                <label for="level_id" class="form-label">المستوى</label>
                <select name="level_id" id="level_id" class="form-control" onchange="this.form.submit()">
                    <option value="0">الكل</option>
                    <?php foreach ($levels as $level): ?>
                    <option value="<?php echo $level['id']; ?>" <?php echo ($filter_level == $level['id']) ? 'selected' : ''; ?>>
                        <?php echo $level['name']; ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
    </form>
</div>

<!-- Results Table -->
<div class="table-container">
    <div class="table-header">
        <h2 class="table-title">قائمة النتائج</h2>
        <div class="table-actions">
            <input type="text" class="table-search-input form-control" placeholder="بحث...">
        </div>
    </div>
    
    <?php if (count($results) > 0): ?>
    <table class="data-table">
        <thead>
            <tr>
                <th data-sortable="true">رقم الطالب</th>
                <th data-sortable="true">اسم الطالب</th>
                <th data-sortable="true">المرحلة</th>
                <th data-sortable="true">المستوى</th>
                <th data-sortable="true">المادة</th>
                <th data-sortable="true">العام الدراسي</th>
                <th data-sortable="true">الفصل الدراسي</th>
                <th data-sortable="true">الدرجة</th>
                <th data-sortable="true">التقدير</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($results as $result): ?>
            <tr>
                <td><?php echo $result['student_code']; ?></td>
                <td><?php echo $result['student_name']; ?></td>
                <td><?php echo $result['stage_name']; ?></td>
                <td><?php echo $result['level_name']; ?></td>
                <td><?php echo $result['subject_name']; ?></td>
                <td><?php echo $result['academic_year']; ?></td>
                <td>
                    <?php 
                    if ($result['semester'] === 'first') echo 'الفصل الأول';
                    elseif ($result['semester'] === 'second') echo 'الفصل الثاني';
                    else echo 'النهائي';
                    ?>
                </td>
                <td><?php echo $result['mark']; ?></td>
                <td>
                    <?php 
                    if ($result['mark'] >= 90) echo 'ممتاز';
                    elseif ($result['mark'] >= 80) echo 'جيد جداً';
                    elseif ($result['mark'] >= 70) echo 'جيد';
                    elseif ($result['mark'] >= 60) echo 'مقبول';
                    elseif ($result['mark'] >= 50) echo 'ضعيف';
                    else echo 'راسب';
                    ?>
                </td>
                <td class="action-buttons">
                    <a href="results.php?action=edit&id=<?php echo $result['id']; ?>" class="btn btn-secondary btn-sm" title="تعديل"><i class="fas fa-edit"></i></a>
                    <a href="results.php?action=delete&id=<?php echo $result['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirmDelete(event);" title="حذف"><i class="fas fa-trash"></i></a>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
    <?php else: ?>
    <p class="no-data">لا توجد نتائج متطابقة مع معايير البحث</p>
    <?php endif; ?>
</div>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
<!-- Add/Edit Result Form -->
<div class="page-header">
    <h1 class="page-title"><?php echo ($action === 'add') ? 'إضافة نتيجة جديدة' : 'تعديل النتيجة'; ?></h1>
    <div class="page-actions">
        <a href="results.php" class="btn btn-secondary"><i class="fas fa-arrow-right"></i> العودة للقائمة</a>
    </div>
</div>

<div class="form-container">
    <form action="results.php?action=<?php echo $action; ?><?php echo ($id > 0) ? '&id=' . $id : ''; ?>" method="post" class="needs-validation">
        <div class="form-row">
            <div class="form-col">
                <label for="student_id" class="form-label">الطالب *</label>
                <select name="student_id" id="student_id" class="form-control" required>
                    <option value="">اختر الطالب</option>
                    <?php foreach ($allStudents as $student): ?>
                    <option value="<?php echo $student['id']; ?>" <?php echo ($action === 'edit' && $result['student_id'] == $student['id']) ? 'selected' : ''; ?>>
                        <?php echo $student['full_name'] . ' (' . $student['code'] . ') - ' . $student['stage'] . ' - ' . $student['level']; ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-col">
                <label for="exam_id" class="form-label">الاختبار *</label>
                <select name="exam_id" id="exam_id" class="form-control" required>
                    <option value="">اختر الاختبار</option>
                    <?php foreach ($allExams as $exam): ?>
                    <option value="<?php echo $exam['id']; ?>" <?php echo ($action === 'edit' && $result['exam_id'] == $exam['id']) ? 'selected' : ''; ?>>
                        <?php echo $exam['subject'] . ' - ' . $exam['stage'] . ' - ' . $exam['level'] . ' (' . $exam['academic_year'] . ' - ' . ($exam['semester'] === 'first' ? 'الفصل الأول' : ($exam['semester'] === 'second' ? 'الفصل الثاني' : 'النهائي')) . ')'; ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-col">
                <label for="subject_id" class="form-label">المادة *</label>
                <select name="subject_id" id="subject_id" class="form-control" required>
                    <option value="">اختر المادة</option>
                    <?php foreach ($allSubjects as $subject): ?>
                    <option value="<?php echo $subject['id']; ?>" <?php echo ($action === 'edit' && $result['subject_id'] == $subject['id']) ? 'selected' : ''; ?>>
                        <?php echo $subject['name'] . ' - ' . $subject['stage'] . ' - ' . $subject['level']; ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-col">
                <label for="mark" class="form-label">الدرجة *</label>
                <input type="number" class="form-control" id="mark" name="mark" value="<?php echo ($action === 'edit') ? $result['mark'] : ''; ?>" min="0" max="100" step="0.01" required>
            </div>
        </div>
        
        <div class="form-group mt-4">
            <button type="submit" class="btn btn-primary"><?php echo ($action === 'add') ? 'إضافة النتيجة' : 'تحديث النتيجة'; ?></button>
            <a href="results.php" class="btn btn-secondary">إلغاء</a>
        </div>
    </form>
</div>

<?php elseif ($action === 'batch_add' && $examDetails): ?>
<!-- Batch Add Results Form -->
<div class="page-header">
    <h1 class="page-title">إدخال نتائج الطلاب</h1>
    <div class="page-actions">
        <a href="exams.php" class="btn btn-secondary"><i class="fas fa-arrow-right"></i> العودة للاختبارات</a>
    </div>
</div>

<div class="form-container">
    <div class="exam-info">
        <div class="info-row">
            <div class="info-item">
                <span class="info-label">المادة:</span>
                <span class="info-value"><?php echo $examDetails['subject_name']; ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">المرحلة:</span>
                <span class="info-value"><?php echo $examDetails['stage_name']; ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">المستوى:</span>
                <span class="info-value"><?php echo $examDetails['level_name']; ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">العام الدراسي:</span>
                <span class="info-value"><?php echo $examDetails['academic_year']; ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">الفصل الدراسي:</span>
                <span class="info-value">
                    <?php 
                    if ($examDetails['semester'] === 'first') echo 'الفصل الأول';
                    elseif ($examDetails['semester'] === 'second') echo 'الفصل الثاني';
                    else echo 'النهائي';
                    ?>
                </span>
            </div>
            <div class="info-item">
                <span class="info-label">تاريخ الاختبار:</span>
                <span class="info-value"><?php echo date('Y-m-d', strtotime($examDetails['exam_date'])); ?></span>
            </div>
        </div>
    </div>
    
    <?php if (count($students) > 0): ?>
    <form action="results.php?action=batch_add&exam_id=<?php echo $exam_id; ?>" method="post" class="needs-validation" id="batchForm">
        <input type="hidden" name="subject_id" value="<?php echo $examDetails['subject_id']; ?>">
        
        <div class="table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th data-sortable="true">رقم الطالب</th>
                        <th data-sortable="true">اسم الطالب</th>
                        <th>الدرجة</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($students as $index => $student): ?>
                    <tr>
                        <td><?php echo $student['student_id']; ?></td>
                        <td><?php echo $student['full_name']; ?></td>
                        <td>
                            <input type="hidden" name="student_ids[]" value="<?php echo $student['id']; ?>">
                            <input type="number" class="form-control student-mark" name="marks[]" value="<?php echo $student['existing_mark'] ?? ''; ?>" min="0" max="100" step="0.01">
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <div class="form-group mt-4">
            <button type="submit" class="btn btn-primary">حفظ جميع النتائج</button>
            <a href="exams.php" class="btn btn-secondary">إلغاء</a>
        </div>
    </form>
    <?php else: ?>
    <p class="no-data">لا يوجد طلاب في هذا المستوى</p>
    <?php endif; ?>
</div>
<?php endif; ?>

<?php include 'includes/footer.php'; ?> 