<?php
// Include database configuration
require_once 'database/config.php';

// Authentication functions
function login($username, $password) {
    $sql = "SELECT * FROM users WHERE username = ?";
    $user = getRow($sql, [$username]);
    
    if ($user && password_verify($password, $user['password'])) {
        // Set session variables
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['full_name'] = $user['full_name'];
        $_SESSION['user_role'] = $user['role'];
        return true;
    }
    
    return false;
}

function logout() {
    // Unset all session variables
    session_unset();
    
    // Destroy the session
    session_destroy();
}

function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function isAdmin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'admin';
}

function isTeacher() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'teacher';
}

// Redirect with message
function redirectWithMessage($url, $message, $type = 'success') {
    $_SESSION[$type . '_message'] = $message;
    header("Location: $url");
    exit;
}

// Students functions
function getStudentDetails($student_id) {
    $sql = "SELECT s.*, u.full_name, u.username, st.name as stage_name, l.name as level_name, 
           sp.name as specialization_name 
           FROM students s 
           JOIN users u ON s.user_id = u.id 
           JOIN stages st ON s.stage_id = st.id 
           JOIN levels l ON s.level_id = l.id 
           LEFT JOIN specializations sp ON s.specialization_id = sp.id 
           WHERE s.id = ?";
    
    return getRow($sql, [$student_id]);
}

function getStudentsByLevel($level_id) {
    $sql = "SELECT s.*, u.full_name 
           FROM students s 
           JOIN users u ON s.user_id = u.id 
           WHERE s.level_id = ? AND s.status = 'active'
           ORDER BY u.full_name";
    
    return getRows($sql, [$level_id]);
}

function getStudentsBySpecialization($specialization_id) {
    $sql = "SELECT s.*, u.full_name 
           FROM students s 
           JOIN users u ON s.user_id = u.id 
           WHERE s.specialization_id = ? AND s.status = 'active'
           ORDER BY u.full_name";
    
    return getRows($sql, [$specialization_id]);
}

// Subjects functions
function getSubjectsByLevel($level_id, $stage_id) {
    $sql = "SELECT * FROM subjects WHERE level_id = ? AND stage_id = ?";
    
    return getRows($sql, [$level_id, $stage_id]);
}

function getSubjectsBySpecialization($specialization_id, $level_id) {
    $sql = "SELECT * FROM subjects 
           WHERE specialization_id = ? AND level_id = ?";
    
    return getRows($sql, [$specialization_id, $level_id]);
}

// Results functions
function getStudentResults($student_id, $academic_year = null) {
    $params = [$student_id];
    $sql = "SELECT er.*, s.name as subject_name, e.academic_year, e.semester 
           FROM exam_results er 
           JOIN exams e ON er.exam_id = e.id 
           JOIN subjects s ON er.subject_id = s.id 
           WHERE er.student_id = ?";
    
    if ($academic_year) {
        $sql .= " AND e.academic_year = ?";
        $params[] = $academic_year;
    }
    
    $sql .= " ORDER BY e.academic_year DESC, e.semester, s.name";
    
    return getRows($sql, $params);
}

function calculateStudentStatus($student_id, $academic_year) {
    // Get all student results for the academic year
    $sql = "SELECT er.*, s.name as subject_name, s.pass_mark 
           FROM exam_results er 
           JOIN exams e ON er.exam_id = e.id 
           JOIN subjects s ON er.subject_id = s.id 
           WHERE er.student_id = ? AND e.academic_year = ?";
    
    $results = getRows($sql, [$student_id, $academic_year]);
    
    // Count failed subjects
    $failedSubjects = 0;
    foreach ($results as $result) {
        if ($result['mark'] < $result['pass_mark']) {
            $failedSubjects++;
        }
    }
    
    // Determine status
    if ($failedSubjects == 0) {
        return 'ناجح';
    } elseif ($failedSubjects <= 2) {
        return 'منقول بمواد';
    } else {
        return 'باقي للإعادة';
    }
}

function progressStudent($student_id, $academic_year, $status) {
    // Get current student stage and level
    $student = getStudentDetails($student_id);
    
    // Default to the same level (for 'باقي للإعادة')
    $new_stage_id = $student['stage_id'];
    $new_level_id = $student['level_id'];
    $new_specialization_id = $student['specialization_id'];
    
    // If student is passing or conditionally passing
    if ($status != 'باقي للإعادة') {
        // Get next level in the same stage
        $sql = "SELECT * FROM levels WHERE stage_id = ? AND id > ? ORDER BY id LIMIT 1";
        $next_level = getRow($sql, [$student['stage_id'], $student['level_id']]);
        
        if ($next_level) {
            // Move to next level in the same stage
            $new_level_id = $next_level['id'];
        } else {
            // No more levels in this stage, check if we need to move to next stage
            $sql = "SELECT * FROM stages WHERE id > ? ORDER BY id LIMIT 1";
            $next_stage = getRow($sql, [$student['stage_id']]);
            
            if ($next_stage) {
                // Move to next stage, first level
                $new_stage_id = $next_stage['id'];
                
                // Get the first level of the new stage
                $sql = "SELECT * FROM levels WHERE stage_id = ? ORDER BY id LIMIT 1";
                $first_level = getRow($sql, [$new_stage_id]);
                
                if ($first_level) {
                    $new_level_id = $first_level['id'];
                }
                
                // If moving to specialized stage, need to set specialization
                if ($next_stage['name'] == 'تخصصية') {
                    // Default to first specialization, should be selected by user in UI
                    $sql = "SELECT * FROM specializations ORDER BY id LIMIT 1";
                    $first_specialization = getRow($sql, []);
                    
                    if ($first_specialization) {
                        $new_specialization_id = $first_specialization['id'];
                    }
                }
            }
        }
    }
    
    // Record the progression
    $sql = "INSERT INTO student_progression (
                student_id, academic_year, old_stage_id, old_level_id, 
                new_stage_id, new_level_id, old_specialization_id, 
                new_specialization_id, status, failed_subjects_count
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $student_id, 
        $academic_year, 
        $student['stage_id'], 
        $student['level_id'], 
        $new_stage_id, 
        $new_level_id, 
        $student['specialization_id'], 
        $new_specialization_id, 
        $status, 
        ($status == 'ناجح' ? 0 : ($status == 'منقول بمواد' ? 1 : 3))
    ];
    
    $progression_id = insertData($sql, $params);
    
    // Update student record with new level and seat number progression
    if ($status != 'باقي للإعادة') {
        // Store current seat number as previous and keep current seat number for new students
        $sql = "UPDATE students SET 
                stage_id = ?, 
                level_id = ?, 
                specialization_id = ?,
                previous_seat_number = current_seat_number,
                student_status = ? 
                WHERE id = ?";
        
        // If student progresses, they become 'مستجد' in the new level
        updateData($sql, [$new_stage_id, $new_level_id, $new_specialization_id, 'مستجد', $student_id]);
    } else {
        // For students repeating the level, update their status to 'معيد'
        $sql = "UPDATE students SET 
                student_status = ? 
                WHERE id = ?";
        
        updateData($sql, ['معيد', $student_id]);
    }
    
    return $progression_id;
}

// Get academic years
function getAcademicYears() {
    $sql = "SELECT DISTINCT academic_year FROM exams ORDER BY academic_year DESC";
    $years = getRows($sql);
    
    $academic_years = [];
    foreach ($years as $year) {
        $academic_years[] = $year['academic_year'];
    }
    
    return $academic_years;
}

// Get all stages
function getAllStages() {
    $sql = "SELECT * FROM stages ORDER BY id";
    return getRows($sql);
}

// Get all levels by stage
function getLevelsByStage($stage_id) {
    $sql = "SELECT * FROM levels WHERE stage_id = ? ORDER BY id";
    return getRows($sql, [$stage_id]);
}

// Get all specializations
function getAllSpecializations() {
    $sql = "SELECT * FROM specializations ORDER BY id";
    return getRows($sql);
}

// Generate current academic year in format 'YYYY-YYYY'
function getCurrentAcademicYear() {
    $current_month = date('n');
    $current_year = date('Y');
    
    // Academic year starts in September
    if ($current_month >= 9) {
        return $current_year . '-' . ($current_year + 1);
    } else {
        return ($current_year - 1) . '-' . $current_year;
    }
}

// Get student count by level
function getStudentCountByLevel($level_id) {
    $sql = "SELECT COUNT(*) as count FROM students WHERE level_id = ? AND status = 'active'";
    $result = getRow($sql, [$level_id]);
    return $result['count'];
}

// Get student count by specialization
function getStudentCountBySpecialization($specialization_id) {
    $sql = "SELECT COUNT(*) as count FROM students WHERE specialization_id = ? AND status = 'active'";
    $result = getRow($sql, [$specialization_id]);
    return $result['count'];
}

// Format and clean user input
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}
?> 