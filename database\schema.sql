-- Database schema for <PERSON><PERSON><PERSON><PERSON> Al<PERSON><PERSON><PERSON> Al-<PERSON><PERSON>ah wal-Arabia Student Management System

-- Create database
CREATE DATABASE IF NOT EXISTS ruwaq_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE ruwaq_db;

-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'teacher', 'student') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Educational stages table
CREATE TABLE stages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT
);

-- Educational levels table
CREATE TABLE levels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    stage_id INT NOT NULL,
    FOREIGN KEY (stage_id) REFERENCES stages(id) ON DELETE CASCADE
);

-- Specializations table
CREATE TABLE specializations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT
);

-- Subjects table
CREATE TABLE subjects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    level_id INT NOT NULL,
    stage_id INT NOT NULL,
    specialization_id INT NULL,
    full_mark INT DEFAULT 100,
    pass_mark INT DEFAULT 50,
    FOREIGN KEY (level_id) REFERENCES levels(id) ON DELETE CASCADE,
    FOREIGN KEY (stage_id) REFERENCES stages(id) ON DELETE CASCADE,
    FOREIGN KEY (specialization_id) REFERENCES specializations(id) ON DELETE SET NULL
);

-- Students table
CREATE TABLE students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    student_id VARCHAR(20) NOT NULL UNIQUE,
    stage_id INT NOT NULL,
    level_id INT NOT NULL,
    specialization_id INT NULL,
    status ENUM('active', 'graduated', 'withdrawn') DEFAULT 'active',
    academic_year VARCHAR(20) NOT NULL,
    enrollment_date DATE NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (stage_id) REFERENCES stages(id) ON DELETE RESTRICT,
    FOREIGN KEY (level_id) REFERENCES levels(id) ON DELETE RESTRICT,
    FOREIGN KEY (specialization_id) REFERENCES specializations(id) ON DELETE SET NULL
);

-- Exams table
CREATE TABLE exams (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subject_id INT NOT NULL,
    academic_year VARCHAR(20) NOT NULL,
    semester ENUM('first', 'second', 'final') NOT NULL,
    exam_date DATE NOT NULL,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
);

-- Exam results table
CREATE TABLE exam_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    exam_id INT NOT NULL,
    subject_id INT NOT NULL,
    mark DECIMAL(5,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (exam_id) REFERENCES exams(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
);

-- Student progression record
CREATE TABLE student_progression (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    academic_year VARCHAR(20) NOT NULL,
    old_stage_id INT NOT NULL,
    old_level_id INT NOT NULL,
    new_stage_id INT NOT NULL,
    new_level_id INT NOT NULL,
    old_specialization_id INT NULL,
    new_specialization_id INT NULL,
    status ENUM('ناجح', 'منقول بمواد', 'باقي للإعادة') NOT NULL,
    failed_subjects_count INT DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (old_stage_id) REFERENCES stages(id) ON DELETE RESTRICT,
    FOREIGN KEY (old_level_id) REFERENCES levels(id) ON DELETE RESTRICT,
    FOREIGN KEY (new_stage_id) REFERENCES stages(id) ON DELETE RESTRICT,
    FOREIGN KEY (new_level_id) REFERENCES levels(id) ON DELETE RESTRICT,
    FOREIGN KEY (old_specialization_id) REFERENCES specializations(id) ON DELETE SET NULL,
    FOREIGN KEY (new_specialization_id) REFERENCES specializations(id) ON DELETE SET NULL
);

-- Insert initial educational stages
INSERT INTO stages (name, description) VALUES 
('تمهيدية', 'المرحلة التمهيدية'),
('متوسطة', 'المرحلة المتوسطة'),
('تخصصية', 'المرحلة التخصصية');

-- Insert levels for each stage
INSERT INTO levels (name, stage_id) VALUES 
('المستوى الأول', 1),
('المستوى الثاني', 1),
('المستوى الأول', 2),
('المستوى الثاني', 2);

-- Insert specializations
INSERT INTO specializations (name, description) VALUES 
('فقه', 'تخصص الفقه'),
('تفسير وحديث', 'تخصص التفسير والحديث'),
('عقيدة', 'تخصص العقيدة'),
('لغة عربية', 'تخصص اللغة العربية');

-- Insert levels for specialized stage
INSERT INTO levels (name, stage_id) VALUES 
('المستوى الأول', 3),
('المستوى الثاني', 3),
('المستوى الثالث', 3),
('المستوى الرابع', 3);

-- Add admin user
INSERT INTO users (username, password, full_name, role) VALUES 
('admin', '$2y$10$VH4oUJ/aXo.qOCxqNDJzoeA4Au29fOCJ3RGdo05dOJFuDDqV7s6CK', 'مدير النظام', 'admin'); 