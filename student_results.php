<?php
require_once 'includes/functions.php';
include 'includes/header.php';

// Get student ID from query string
$student_id = isset($_GET['student_id']) ? intval($_GET['student_id']) : 0;

if ($student_id <= 0) {
    $_SESSION['error_message'] = 'معرف الطالب غير صحيح';
    header('Location: students.php');
    exit;
}

// Get student details
$student = getStudentDetails($student_id);

if (!$student) {
    $_SESSION['error_message'] = 'الطالب غير موجود';
    header('Location: students.php');
    exit;
}

// Get filter values
$filter_year = isset($_GET['academic_year']) ? sanitizeInput($_GET['academic_year']) : '';

// Get student's results
$results = getStudentResults($student_id, $filter_year);

// Get all academic years for filter dropdown
$academic_years = getAcademicYears();

// Calculate averages and counts
$totalMarks = 0;
$passedSubjects = 0;
$failedSubjects = 0;

foreach ($results as $result) {
    $totalMarks += $result['mark'];
    
    if ($result['mark'] >= 50) {
        $passedSubjects++;
    } else {
        $failedSubjects++;
    }
}

$average = count($results) > 0 ? round($totalMarks / count($results), 2) : 0;

// Determine student status for the selected academic year
$status = '';
if (!empty($filter_year) && count($results) > 0) {
    $status = calculateStudentStatus($student_id, $filter_year);
}
?>

<div class="page-header">
    <h1 class="page-title">نتائج الطالب: <?php echo $student['full_name']; ?></h1>
    <div class="page-actions">
        <a href="students.php" class="btn btn-secondary"><i class="fas fa-arrow-right"></i> العودة للقائمة</a>
        <button onclick="printContent('printable-content')" class="btn btn-primary"><i class="fas fa-print"></i> طباعة النتيجة</button>
    </div>
</div>

<div class="form-container">
    <div class="student-info">
        <div class="info-row">
            <div class="info-item">
                <span class="info-label">رقم الطالب:</span>
                <span class="info-value"><?php echo $student['student_id']; ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">المرحلة:</span>
                <span class="info-value"><?php echo $student['stage_name']; ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">المستوى:</span>
                <span class="info-value"><?php echo $student['level_name']; ?></span>
            </div>
            <?php if ($student['specialization_name']): ?>
            <div class="info-item">
                <span class="info-label">التخصص:</span>
                <span class="info-value"><?php echo $student['specialization_name']; ?></span>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="form-container">
    <h2 class="form-title">تصفية النتائج</h2>
    <form action="student_results.php" method="get" class="filter-form">
        <input type="hidden" name="student_id" value="<?php echo $student_id; ?>">
        
        <div class="form-row">
            <div class="form-col">
                <label for="academic_year" class="form-label">العام الدراسي</label>
                <select name="academic_year" id="academic_year" class="form-control" onchange="this.form.submit()">
                    <option value="">كل الأعوام</option>
                    <?php foreach ($academic_years as $year): ?>
                    <option value="<?php echo $year; ?>" <?php echo ($filter_year == $year) ? 'selected' : ''; ?>>
                        <?php echo $year; ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
    </form>
</div>

<div id="printable-content">
    <!-- Results Summary -->
    <div class="form-container">
        <h2 class="form-title">ملخص النتائج</h2>
        
        <div class="results-summary">
            <div class="summary-row">
                <div class="summary-item">
                    <span class="summary-label">عدد المواد:</span>
                    <span class="summary-value"><?php echo count($results); ?></span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">المواد الناجحة:</span>
                    <span class="summary-value"><?php echo $passedSubjects; ?></span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">المواد الراسبة:</span>
                    <span class="summary-value"><?php echo $failedSubjects; ?></span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">المعدل:</span>
                    <span class="summary-value"><?php echo $average; ?></span>
                </div>
                <?php if (!empty($status)): ?>
                <div class="summary-item">
                    <span class="summary-label">الحالة:</span>
                    <span class="summary-value status-<?php echo ($status === 'ناجح') ? 'success' : (($status === 'منقول بمواد') ? 'warning' : 'danger'); ?>">
                        <?php echo $status; ?>
                    </span>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Results Table -->
    <div class="table-container">
        <h2 class="table-title">تفاصيل النتائج</h2>
        
        <?php if (count($results) > 0): ?>
        <table class="data-table">
            <thead>
                <tr>
                    <th data-sortable="true">المادة</th>
                    <th data-sortable="true">العام الدراسي</th>
                    <th data-sortable="true">الفصل الدراسي</th>
                    <th data-sortable="true">الدرجة</th>
                    <th data-sortable="true">التقدير</th>
                    <th data-sortable="true">الحالة</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($results as $result): ?>
                <tr>
                    <td><?php echo $result['subject_name']; ?></td>
                    <td><?php echo $result['academic_year']; ?></td>
                    <td>
                        <?php 
                        if ($result['semester'] === 'first') echo 'الفصل الأول';
                        elseif ($result['semester'] === 'second') echo 'الفصل الثاني';
                        else echo 'النهائي';
                        ?>
                    </td>
                    <td><?php echo $result['mark']; ?></td>
                    <td>
                        <?php 
                        if ($result['mark'] >= 90) echo 'ممتاز';
                        elseif ($result['mark'] >= 80) echo 'جيد جداً';
                        elseif ($result['mark'] >= 70) echo 'جيد';
                        elseif ($result['mark'] >= 60) echo 'مقبول';
                        elseif ($result['mark'] >= 50) echo 'ضعيف';
                        else echo 'راسب';
                        ?>
                    </td>
                    <td class="<?php echo ($result['mark'] >= 50) ? 'text-success' : 'text-danger'; ?>">
                        <?php echo ($result['mark'] >= 50) ? 'ناجح' : 'راسب'; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php else: ?>
        <p class="no-data">لا توجد نتائج لهذا الطالب</p>
        <?php endif; ?>
    </div>
</div>

<?php include 'includes/footer.php'; ?> 