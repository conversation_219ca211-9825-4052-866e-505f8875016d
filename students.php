<?php
require_once 'includes/functions.php';
include 'includes/header.php';

// Get action from query string
$action = isset($_GET['action']) ? $_GET['action'] : 'list';
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add' || $action === 'edit') {
        // Get form data
        $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
        $student_id = sanitizeInput($_POST['student_id']);
        $full_name = sanitizeInput($_POST['full_name']);
        $national_id = sanitizeInput($_POST['national_id']);
        $phone_number = sanitizeInput($_POST['phone_number']);
        $previous_seat_number = sanitizeInput($_POST['previous_seat_number']);
        $current_seat_number = sanitizeInput($_POST['current_seat_number']);
        $stage_id = intval($_POST['stage_id']);
        $level_id = intval($_POST['level_id']);
        $specialization_id = (isset($_POST['specialization_id']) && $_POST['specialization_id'] > 0) ? intval($_POST['specialization_id']) : null;
        $juristic_school = sanitizeInput($_POST['juristic_school']);
        $student_status = sanitizeInput($_POST['student_status']);
        $academic_year = sanitizeInput($_POST['academic_year']);
        $status = sanitizeInput($_POST['status']);
        $enrollment_date = sanitizeInput($_POST['enrollment_date']);
        
        // Validate data
        $errors = [];
        if (empty($student_id)) {
            $errors[] = 'يجب إدخال رقم الطالب';
        }
        if (empty($full_name)) {
            $errors[] = 'يجب إدخال اسم الطالب';
        }
        if (empty($national_id)) {
            $errors[] = 'يجب إدخال الرقم القومي';
        }
        if (empty($phone_number)) {
            $errors[] = 'يجب إدخال رقم التليفون';
        }
        if (empty($current_seat_number)) {
            $errors[] = 'يجب إدخال رقم الجلوس الحالي';
        }
        if ($stage_id <= 0) {
            $errors[] = 'يجب اختيار المرحلة';
        }
        if ($level_id <= 0) {
            $errors[] = 'يجب اختيار المستوى';
        }
        if (empty($juristic_school)) {
            $errors[] = 'يجب اختيار المذهب الفقهي';
        }
        if (empty($student_status)) {
            $errors[] = 'يجب اختيار حالة الطالب (مستجد/معيد)';
        }
        if (empty($academic_year)) {
            $errors[] = 'يجب إدخال العام الدراسي';
        }
        if (empty($enrollment_date)) {
            $errors[] = 'يجب إدخال تاريخ التسجيل';
        }
        
        if (empty($errors)) {
            $conn = connectDB();
            
            // For adding a new student
            if ($action === 'add') {
                // First, create the user account
                $username = $student_id; // Use student ID as username
                $password = password_hash($student_id, PASSWORD_DEFAULT); // Use student ID as initial password
                
                $sql = "INSERT INTO users (username, password, full_name, role) VALUES (?, ?, ?, 'student')";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param('sss', $username, $password, $full_name);
                $stmt->execute();
                $user_id = $conn->insert_id;
                $stmt->close();
                
                // Then, create the student record
                $sql = "INSERT INTO students (user_id, student_id, full_name, national_id, phone_number, previous_seat_number, current_seat_number, 
                       stage_id, level_id, specialization_id, juristic_school, student_status, status, academic_year, enrollment_date) 
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param('issssssiiissss', $user_id, $student_id, $full_name, $national_id, $phone_number, $previous_seat_number, 
                                $current_seat_number, $stage_id, $level_id, $specialization_id, $juristic_school, $student_status, $status, 
                                $academic_year, $enrollment_date);
                $stmt->execute();
                $stmt->close();
                
                $_SESSION['success_message'] = 'تمت إضافة الطالب بنجاح';
                header('Location: students.php');
                exit;
            } 
            // For updating an existing student
            else if ($action === 'edit' && $id > 0) {
                // Update the user account
                $sql = "UPDATE users SET full_name = ? WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param('si', $full_name, $user_id);
                $stmt->execute();
                $stmt->close();
                
                // Update the student record
                $sql = "UPDATE students SET 
                       student_id = ?, 
                       full_name = ?, 
                       national_id = ?, 
                       phone_number = ?, 
                       previous_seat_number = ?, 
                       current_seat_number = ?, 
                       stage_id = ?, 
                       level_id = ?, 
                       specialization_id = ?, 
                       juristic_school = ?, 
                       student_status = ?, 
                       status = ?, 
                       academic_year = ?, 
                       enrollment_date = ? 
                       WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param('ssssssiiiisssi', $student_id, $full_name, $national_id, $phone_number, $previous_seat_number, 
                                $current_seat_number, $stage_id, $level_id, $specialization_id, $juristic_school, $student_status, 
                                $status, $academic_year, $enrollment_date, $id);
                $stmt->execute();
                $stmt->close();
                
                $_SESSION['success_message'] = 'تم تحديث بيانات الطالب بنجاح';
                header('Location: students.php');
                exit;
            }
            
            closeDB($conn);
        } else {
            $_SESSION['error_message'] = implode('<br>', $errors);
        }
    } 
    // Handle delete action
    else if ($action === 'delete' && $id > 0) {
        $conn = connectDB();
        
        // Get user_id before deleting student
        $sql = "SELECT user_id FROM students WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('i', $id);
        $stmt->execute();
        $result = $stmt->get_result();
        $student = $result->fetch_assoc();
        $user_id = $student['user_id'];
        $stmt->close();
        
        // Delete the student record
        $sql = "DELETE FROM students WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('i', $id);
        $stmt->execute();
        $stmt->close();
        
        // Delete the user account
        $sql = "DELETE FROM users WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('i', $user_id);
        $stmt->execute();
        $stmt->close();
        
        closeDB($conn);
        
        $_SESSION['success_message'] = 'تم حذف الطالب بنجاح';
        header('Location: students.php');
        exit;
    }
}

// Get data for display
$conn = connectDB();

// For edit action
$student = null;
if ($action === 'edit' && $id > 0) {
    $sql = "SELECT s.*, u.full_name 
           FROM students s 
           JOIN users u ON s.user_id = u.id 
           WHERE s.id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $result = $stmt->get_result();
    $student = $result->fetch_assoc();
    $stmt->close();
    
    if (!$student) {
        $_SESSION['error_message'] = 'الطالب غير موجود';
        header('Location: students.php');
        exit;
    }
}

// For list action
$students = [];
if ($action === 'list') {
    // Get filter values
    $filter_stage = isset($_GET['stage']) ? intval($_GET['stage']) : 0;
    $filter_level = isset($_GET['level']) ? intval($_GET['level']) : 0;
    $filter_specialization = isset($_GET['specialization']) ? intval($_GET['specialization']) : 0;
    $filter_status = isset($_GET['student_status']) ? sanitizeInput($_GET['student_status']) : '';
    
    // Build the query
    $sql = "SELECT s.*, u.full_name, st.name as stage_name, l.name as level_name, sp.name as specialization_name 
           FROM students s 
           JOIN users u ON s.user_id = u.id 
           JOIN stages st ON s.stage_id = st.id 
           JOIN levels l ON s.level_id = l.id 
           LEFT JOIN specializations sp ON s.specialization_id = sp.id 
           WHERE 1=1";
    
    $params = [];
    $types = '';
    
    if ($filter_stage > 0) {
        $sql .= " AND s.stage_id = ?";
        $params[] = $filter_stage;
        $types .= 'i';
    }
    
    if ($filter_level > 0) {
        $sql .= " AND s.level_id = ?";
        $params[] = $filter_level;
        $types .= 'i';
    }
    
    if ($filter_specialization > 0) {
        $sql .= " AND s.specialization_id = ?";
        $params[] = $filter_specialization;
        $types .= 'i';
    }
    
    if (!empty($filter_status)) {
        $sql .= " AND s.student_status = ?";
        $params[] = $filter_status;
        $types .= 's';
    }
    
    $sql .= " ORDER BY s.current_seat_number, u.full_name";
    
    $stmt = $conn->prepare($sql);
    
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        $students[] = $row;
    }
    
    $stmt->close();
}

// Get all stages, levels, and specializations for dropdowns
$stages = getAllStages();
$levels = [];
$specializations = getAllSpecializations();

// If stage is selected, get its levels
if (isset($_GET['stage']) && $_GET['stage'] > 0) {
    $levels = getLevelsByStage($_GET['stage']);
}

closeDB($conn);
?>

<?php if ($action === 'list'): ?>
<!-- Students List -->
<div class="page-header">
    <h1 class="page-title">إدارة الطلاب</h1>
    <div class="page-actions">
        <a href="students.php?action=add" class="btn btn-primary"><i class="fas fa-user-plus"></i> إضافة طالب جديد</a>
    </div>
</div>

<!-- Filters -->
<div class="form-container">
    <h2 class="form-title">تصفية النتائج</h2>
    <form action="students.php" method="get" class="filter-form">
        <div class="form-row">
            <div class="form-col">
                <label for="stage" class="form-label">المرحلة</label>
                <select name="stage" id="stage" class="form-control" onchange="this.form.submit()">
                    <option value="0">الكل</option>
                    <?php foreach ($stages as $stage): ?>
                    <option value="<?php echo $stage['id']; ?>" <?php echo ($filter_stage == $stage['id']) ? 'selected' : ''; ?>>
                        <?php echo $stage['name']; ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-col">
                <label for="level" class="form-label">المستوى</label>
                <select name="level" id="level" class="form-control" onchange="this.form.submit()">
                    <option value="0">الكل</option>
                    <?php foreach ($levels as $level): ?>
                    <option value="<?php echo $level['id']; ?>" <?php echo ($filter_level == $level['id']) ? 'selected' : ''; ?>>
                        <?php echo $level['name']; ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-col">
                <label for="specialization" class="form-label">التخصص</label>
                <select name="specialization" id="specialization" class="form-control" onchange="this.form.submit()">
                    <option value="0">الكل</option>
                    <?php foreach ($specializations as $specialization): ?>
                    <option value="<?php echo $specialization['id']; ?>" <?php echo ($filter_specialization == $specialization['id']) ? 'selected' : ''; ?>>
                        <?php echo $specialization['name']; ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-col">
                <label for="student_status" class="form-label">حالة الطالب</label>
                <select name="student_status" id="student_status" class="form-control" onchange="this.form.submit()">
                    <option value="">الكل</option>
                    <option value="مستجد" <?php echo ($filter_status == 'مستجد') ? 'selected' : ''; ?>>مستجد</option>
                    <option value="معيد" <?php echo ($filter_status == 'معيد') ? 'selected' : ''; ?>>معيد</option>
                </select>
            </div>
        </div>
    </form>
</div>

<!-- Students Table -->
<div class="table-container">
    <div class="table-header">
        <h2 class="table-title">قائمة الطلاب</h2>
        <div class="table-actions">
            <input type="text" class="table-search-input form-control" placeholder="بحث...">
        </div>
    </div>
    
    <?php if (count($students) > 0): ?>
    <table class="data-table">
        <thead>
            <tr>
                <th>م</th>
                <th data-sortable="true">رقم الجلوس السابق</th>
                <th data-sortable="true">رقم الجلوس الحالي</th>
                <th data-sortable="true">الاسم</th>
                <th data-sortable="true">الرقم القومي</th>
                <th data-sortable="true">رقم التليفون</th>
                <th data-sortable="true">المرحلة</th>
                <th data-sortable="true">المستوى</th>
                <th data-sortable="true">التخصص</th>
                <th data-sortable="true">المذهب الفقهي</th>
                <th data-sortable="true">الحالة</th>
                <th data-sortable="true">العام الدراسي</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php 
            $counter = 1;
            foreach ($students as $student): 
            ?>
            <tr>
                <td><?php echo $counter++; ?></td>
                <td><?php echo $student['previous_seat_number'] ?: '-'; ?></td>
                <td><?php echo $student['current_seat_number']; ?></td>
                <td><?php echo $student['full_name']; ?></td>
                <td><?php echo $student['national_id']; ?></td>
                <td><?php echo $student['phone_number']; ?></td>
                <td><?php echo $student['stage_name']; ?></td>
                <td><?php echo $student['level_name']; ?></td>
                <td><?php echo $student['specialization_name'] ?? '-'; ?></td>
                <td><?php echo $student['juristic_school']; ?></td>
                <td><?php echo $student['student_status']; ?></td>
                <td><?php echo $student['academic_year']; ?></td>
                <td class="action-buttons">
                    <a href="student_results.php?student_id=<?php echo $student['id']; ?>" class="btn btn-info btn-sm" title="عرض النتائج"><i class="fas fa-chart-bar"></i></a>
                    <a href="students.php?action=edit&id=<?php echo $student['id']; ?>" class="btn btn-secondary btn-sm" title="تعديل"><i class="fas fa-edit"></i></a>
                    <a href="students.php?action=delete&id=<?php echo $student['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirmDelete(event);" title="حذف"><i class="fas fa-trash"></i></a>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
    <?php else: ?>
    <p class="no-data">لا يوجد طلاب متطابقين مع معايير البحث</p>
    <?php endif; ?>
</div>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
<!-- Add/Edit Student Form -->
<div class="page-header">
    <h1 class="page-title"><?php echo ($action === 'add') ? 'إضافة طالب جديد' : 'تعديل بيانات الطالب'; ?></h1>
    <div class="page-actions">
        <a href="students.php" class="btn btn-secondary"><i class="fas fa-arrow-right"></i> العودة للقائمة</a>
    </div>
</div>

<div class="form-container">
    <form action="students.php?action=<?php echo $action; ?><?php echo ($id > 0) ? '&id=' . $id : ''; ?>" method="post" class="needs-validation">
        <?php if ($action === 'edit'): ?>
        <input type="hidden" name="user_id" value="<?php echo $student['user_id']; ?>">
        <?php endif; ?>
        
        <div class="form-row">
            <div class="form-col">
                <label for="student_id" class="form-label">رقم الطالب *</label>
                <input type="text" class="form-control" id="student_id" name="student_id" value="<?php echo ($action === 'edit') ? $student['student_id'] : ''; ?>" required>
            </div>
            
            <div class="form-col">
                <label for="full_name" class="form-label">اسم الطالب *</label>
                <input type="text" class="form-control" id="full_name" name="full_name" value="<?php echo ($action === 'edit') ? $student['full_name'] : ''; ?>" required>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-col">
                <label for="national_id" class="form-label">الرقم القومي *</label>
                <input type="text" class="form-control" id="national_id" name="national_id" value="<?php echo ($action === 'edit') ? $student['national_id'] : ''; ?>" required>
            </div>
            
            <div class="form-col">
                <label for="phone_number" class="form-label">رقم التليفون *</label>
                <input type="text" class="form-control" id="phone_number" name="phone_number" value="<?php echo ($action === 'edit') ? $student['phone_number'] : ''; ?>" required>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-col">
                <label for="previous_seat_number" class="form-label">رقم الجلوس السابق</label>
                <input type="text" class="form-control" id="previous_seat_number" name="previous_seat_number" value="<?php echo ($action === 'edit') ? $student['previous_seat_number'] : ''; ?>">
            </div>
            
            <div class="form-col">
                <label for="current_seat_number" class="form-label">رقم الجلوس الحالي *</label>
                <input type="text" class="form-control" id="current_seat_number" name="current_seat_number" value="<?php echo ($action === 'edit') ? $student['current_seat_number'] : ''; ?>" required>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-col">
                <label for="stage_id" class="form-label">المرحلة *</label>
                <select name="stage_id" id="stage_id" class="form-control" required onchange="updateLevels()">
                    <option value="">اختر المرحلة</option>
                    <?php foreach ($stages as $stage): ?>
                    <option value="<?php echo $stage['id']; ?>" <?php echo ($action === 'edit' && $student['stage_id'] == $stage['id']) ? 'selected' : ''; ?>>
                        <?php echo $stage['name']; ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-col">
                <label for="level_id" class="form-label">المستوى *</label>
                <select name="level_id" id="level_id" class="form-control" required>
                    <option value="">اختر المستوى</option>
                    <?php 
                    if ($action === 'edit') {
                        $stageLevels = getLevelsByStage($student['stage_id']);
                        foreach ($stageLevels as $level): 
                    ?>
                    <option value="<?php echo $level['id']; ?>" <?php echo ($student['level_id'] == $level['id']) ? 'selected' : ''; ?>>
                        <?php echo $level['name']; ?>
                    </option>
                    <?php 
                        endforeach; 
                    }
                    ?>
                </select>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-col">
                <label for="specialization_id" class="form-label">التخصص</label>
                <select name="specialization_id" id="specialization_id" class="form-control">
                    <option value="">بدون تخصص</option>
                    <?php foreach ($specializations as $specialization): ?>
                    <option value="<?php echo $specialization['id']; ?>" <?php echo ($action === 'edit' && $student['specialization_id'] == $specialization['id']) ? 'selected' : ''; ?>>
                        <?php echo $specialization['name']; ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-col">
                <label for="juristic_school" class="form-label">المذهب الفقهي *</label>
                <select name="juristic_school" id="juristic_school" class="form-control" required>
                    <option value="">اختر المذهب الفقهي</option>
                    <option value="حنفي" <?php echo ($action === 'edit' && $student['juristic_school'] === 'حنفي') ? 'selected' : ''; ?>>حنفي</option>
                    <option value="شافعي" <?php echo ($action === 'edit' && $student['juristic_school'] === 'شافعي') ? 'selected' : ''; ?>>شافعي</option>
                    <option value="مالكي" <?php echo ($action === 'edit' && $student['juristic_school'] === 'مالكي') ? 'selected' : ''; ?>>مالكي</option>
                    <option value="حنبلي" <?php echo ($action === 'edit' && $student['juristic_school'] === 'حنبلي') ? 'selected' : ''; ?>>حنبلي</option>
                </select>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-col">
                <label for="student_status" class="form-label">حالة الطالب *</label>
                <select name="student_status" id="student_status" class="form-control" required>
                    <option value="مستجد" <?php echo ($action === 'edit' && $student['student_status'] === 'مستجد') ? 'selected' : ''; ?>>مستجد</option>
                    <option value="معيد" <?php echo ($action === 'edit' && $student['student_status'] === 'معيد') ? 'selected' : ''; ?>>معيد</option>
                </select>
            </div>
            
            <div class="form-col">
                <label for="status" class="form-label">حالة القيد *</label>
                <select name="status" id="status" class="form-control" required>
                    <option value="active" <?php echo ($action === 'edit' && $student['status'] === 'active') ? 'selected' : ''; ?>>نشط</option>
                    <option value="graduated" <?php echo ($action === 'edit' && $student['status'] === 'graduated') ? 'selected' : ''; ?>>متخرج</option>
                    <option value="withdrawn" <?php echo ($action === 'edit' && $student['status'] === 'withdrawn') ? 'selected' : ''; ?>>منسحب</option>
                </select>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-col">
                <label for="academic_year" class="form-label">العام الدراسي *</label>
                <input type="text" class="form-control" id="academic_year" name="academic_year" value="<?php echo ($action === 'edit') ? $student['academic_year'] : getCurrentAcademicYear(); ?>" required>
            </div>
            
            <div class="form-col">
                <label for="enrollment_date" class="form-label">تاريخ التسجيل *</label>
                <input type="date" class="form-control" id="enrollment_date" name="enrollment_date" value="<?php echo ($action === 'edit') ? $student['enrollment_date'] : date('Y-m-d'); ?>" required>
            </div>
        </div>
        
        <div class="form-group mt-4">
            <button type="submit" class="btn btn-primary"><?php echo ($action === 'add') ? 'إضافة الطالب' : 'تحديث البيانات'; ?></button>
            <a href="students.php" class="btn btn-secondary">إلغاء</a>
        </div>
    </form>
</div>

<!-- JavaScript for dynamic level options -->
<script>
    function updateLevels() {
        const stageId = document.getElementById('stage_id').value;
        const levelSelect = document.getElementById('level_id');
        
        // Clear current options
        levelSelect.innerHTML = '<option value="">اختر المستوى</option>';
        
        if (stageId) {
            // Make AJAX request to get levels for this stage
            fetch('get_levels.php?stage_id=' + stageId)
                .then(response => response.json())
                .then(data => {
                    data.forEach(level => {
                        const option = document.createElement('option');
                        option.value = level.id;
                        option.textContent = level.name;
                        levelSelect.appendChild(option);
                    });
                });
        }
    }
</script>
<?php endif; ?>

<?php include 'includes/footer.php'; ?> 