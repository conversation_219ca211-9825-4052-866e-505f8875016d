-- SQL script to update the students table with new fields

-- Add new columns to students table
ALTER TABLE students 
ADD COLUMN previous_seat_number VARCHAR(20) NULL COMMENT 'رقم الجلوس السابق',
ADD COLUMN current_seat_number VARCHAR(20) NOT NULL COMMENT 'رقم الجلوس الحالي' AFTER previous_seat_number,
ADD COLUMN national_id VARCHAR(20) NOT NULL COMMENT 'الرقم القومي' AFTER full_name,
ADD COLUMN phone_number VARCHAR(20) NOT NULL COMMENT 'رقم التليفون' AFTER national_id,
ADD COLUMN juristic_school ENUM('حنفي', 'شافعي', 'مالكي', 'حنبلي') NOT NULL COMMENT 'المذهب الفقهي' AFTER specialization_id,
ADD COLUMN student_status ENUM('مستجد', 'معيد') NOT NULL DEFAULT 'مستجد' COMMENT 'حالة الطالب' AFTER juristic_school;

-- Create index for faster lookups
CREATE INDEX idx_student_current_seat ON students(current_seat_number);
CREATE INDEX idx_student_national_id ON students(national_id);

-- Add constraint to ensure unique national ID
ALTER TABLE students ADD CONSTRAINT unique_national_id UNIQUE (national_id);

-- Optional: Sample UPDATE statement to populate seat numbers for existing students
-- UPDATE students SET current_seat_number = CONCAT('S', id) WHERE current_seat_number IS NULL; 