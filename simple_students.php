<?php
// استيراد البيانات الوهمية من ملف config.php
require_once 'database/config.php';

// إنشاء كائن قاعدة البيانات الوهمية
$db = new MockDatabase();
$students = $db->mock_data['students'];
$stages = $db->mock_data['stages'];
$levels = $db->mock_data['levels'];
$specializations = $db->mock_data['specializations'];
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معاينة نظام إدارة الطلاب</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            border-radius: 5px;
        }
        header {
            background-color: #005e30;
            color: #fff;
            padding: 10px 20px;
            text-align: center;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        h1, h2 {
            margin-top: 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: right;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .filters {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            background-color: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
        }
        .filter-group {
            flex: 1;
            min-width: 200px;
        }
        .filter-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .filter-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .btn {
            display: inline-block;
            background-color: #005e30;
            color: #fff;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
        }
        .btn:hover {
            background-color: #004020;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>رواق العلوم الشرعية والعربية</h1>
            <h2>معاينة نظام إدارة الطلاب - الحقول الجديدة</h2>
        </header>
        
        <div class="filters">
            <div class="filter-group">
                <label for="stage">المرحلة</label>
                <select id="stage">
                    <option value="0">الكل</option>
                    <?php foreach ($stages as $stage): ?>
                    <option value="<?php echo $stage['id']; ?>"><?php echo $stage['name']; ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="filter-group">
                <label for="level">المستوى</label>
                <select id="level">
                    <option value="0">الكل</option>
                    <?php foreach ($levels as $level): ?>
                    <option value="<?php echo $level['id']; ?>"><?php echo $level['name']; ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="filter-group">
                <label for="specialization">التخصص</label>
                <select id="specialization">
                    <option value="0">الكل</option>
                    <?php foreach ($specializations as $spec): ?>
                    <option value="<?php echo $spec['id']; ?>"><?php echo $spec['name']; ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="filter-group">
                <label for="status">حالة الطالب</label>
                <select id="status">
                    <option value="">الكل</option>
                    <option value="مستجد">مستجد</option>
                    <option value="معيد">معيد</option>
                </select>
            </div>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>م</th>
                    <th>رقم الجلوس السابق</th>
                    <th>رقم الجلوس الحالي</th>
                    <th>الاسم</th>
                    <th>الرقم القومي</th>
                    <th>رقم التليفون</th>
                    <th>المرحلة</th>
                    <th>المستوى</th>
                    <th>التخصص</th>
                    <th>المذهب الفقهي</th>
                    <th>حالة الطالب</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($students as $index => $student): ?>
                <tr>
                    <td><?php echo $index + 1; ?></td>
                    <td><?php echo $student['previous_seat_number'] ?: '-'; ?></td>
                    <td><?php echo $student['current_seat_number']; ?></td>
                    <td><?php echo $student['full_name']; ?></td>
                    <td><?php echo $student['national_id']; ?></td>
                    <td><?php echo $student['phone_number']; ?></td>
                    <td><?php echo $student['stage_name']; ?></td>
                    <td><?php echo $student['level_name']; ?></td>
                    <td><?php echo $student['specialization_name'] ?: '-'; ?></td>
                    <td><?php echo $student['juristic_school']; ?></td>
                    <td><?php echo $student['student_status']; ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <div>
            <p><strong>ملاحظة:</strong> هذه صفحة معاينة بسيطة تظهر الحقول الجديدة التي تم إضافتها لنظام إدارة الطلاب:</p>
            <ul>
                <li>رقم الجلوس السابق (اختياري)</li>
                <li>رقم الجلوس الحالي (إجباري)</li>
                <li>الرقم القومي (إجباري)</li>
                <li>رقم التليفون (إجباري)</li>
                <li>المذهب الفقهي: حنفي / شافعي / مالكي / حنبلي (إجباري)</li>
                <li>حالة الطالب: مستجد / معيد (إجباري)</li>
            </ul>
            <p>يمكن ترحيل رقم الجلوس الحالي ليصبح رقم الجلوس السابق عند الانتقال للمستوى التالي.</p>
        </div>
    </div>
</body>
</html> 